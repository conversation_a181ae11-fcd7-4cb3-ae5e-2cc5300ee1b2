using Microsoft.EntityFrameworkCore;
using SpiritLeadRevival.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;

// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace SpiritLeadRevival.DataAccess.Data
{
    public class SlrmDbContext : IdentityDbContext
    {
        public SlrmDbContext(DbContextOptions<SlrmDbContext> options) : base(options)
        {
        }
        public DbSet<PraiseReport> PraiseReports { get; set; }
        public DbSet<BoardMember> BoardMembers { get; set; }
        public DbSet<NewsItem> NewsItems { get; set; }
        public DbSet<CarouselItem> CarouselItems { get; set; }
        public DbSet<DailyScripture> DailyScriptures { get; set; }
        public DbSet<VideoFile> VideoFiles { get; set; }
        public DbSet<ApplicationUser> ApplicationUsers { get; set; }
        public DbSet<RoadChaplain> RoadChaplains { get; set; }
        public DbSet<PrayerRequest> PrayerRequests { get; set; }
        public DbSet<PrayerRequestRecipient> PrayerRequestRecipients { get; set; }
        public DbSet<RevivalConference> RevivalConferences { get; set; }
        public DbSet<RevivalPrincipalOfFaith> RevivalPrincipalsOfFaiths { get; set; }
        public DbSet<RevivalSpiritLeadDecision> RevivalSpiritLeadDecisions { get; set; }
        public DbSet<RoadChaplainIntroductionVideo> RoadChaplainIntroductionVideos { get; set; }
        public DbSet<RoadChaplainIntroduction> RoadChaplainIntroductions { get; set; }
        public DbSet<RevivalReference> RevivalReferences { get; set; }
        public DbSet<MinistryHelpLink> MinistryHelpLinks { get; set; }
        public DbSet<MinistryDescription> MinistryDescriptions { get; set; }
        public DbSet<RoadChaplainNews> RoadChaplainNews { get; set; }
        public DbSet<StaffMember> StaffMembers { get; set; }
        public DbSet<Sponsor> Sponsors { get; set; }
        public DbSet<Event> Events { get; set; }
        public DbSet<RoadChaplainVideo> RoadChaplainVideos { get; set; }
        public DbSet<HeadRoadChaplain> HeadRoadChaplains { get; set; }
        public DbSet<MailingListContact> MailingList { get; set; }
        public DbSet<Pastor> Pastors { get; set; }


        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder) {
            optionsBuilder.EnableDetailedErrors();
            optionsBuilder.EnableSensitiveDataLogging();
            base.OnConfiguring(optionsBuilder);
        }
    }

}
