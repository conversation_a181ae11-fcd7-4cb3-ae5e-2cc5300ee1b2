# Specify the path to your .NET project file
$projectPath = "\SpiritLeadRevival\SpiritLeadRevival.csproj"

# Specify the path to your publishing profile (modify as needed)
$publishProfilePath = "\SpiritLeadRevival\Properties\PublishProfiles\spiritle-001-site1 - Web Deploy.pubxml"

# Set the desired configuration (e.g., Release)
$configuration = "Release"

# Set the output directory for the published files
$outputDirectory = "C:\path\to\YourOutputDirectory"

# Build and publish the project
dotnet publish $projectPath --configuration $configuration --output $outputDirectory --publish-profile $publishProfilePath
