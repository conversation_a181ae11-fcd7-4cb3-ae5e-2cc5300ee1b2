<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TypeScriptTarget>ES6</TypeScriptTarget>
  </PropertyGroup>

  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <TypeScriptTarget>ES6</TypeScriptTarget>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
	  <LangVersion>latest</LangVersion>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>f6bfe16c-1ee7-463d-95fc-96d13b303894</UserSecretsId>
    <NoWarn>NU1701;1701</NoWarn>
    <SuppressNETCoreSdkPreviewMessage>true</SuppressNETCoreSdkPreviewMessage>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <Optimize>False</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <Optimize>True</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.SEOHelper" Version="1.0.1" />
    <PackageReference Include="AspNetSeo.CoreMvc" Version="1.5.1" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="jQuery" Version="3.7.1" />
    <PackageReference Include="jQuery.UI.Combined" Version="1.13.2" />
    <PackageReference Include="MailKit" Version="4.5.0" />
    <PackageReference Include="Microsoft.AspNet.Mvc" Version="5.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="8.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SpiritLeadRevival.DataAccess\SpiritLeadRevival.DataAccess.csproj" />
    <ProjectReference Include="..\SpiritLeadRevival.Models\SpiritLeadRevival.Models.csproj" />
    <ProjectReference Include="..\SpiritLeadRevival.Repositories\SpiritLeadRevival.Repositories.csproj" />
    <ProjectReference Include="..\SpiritLeadRevival.Utilities\SpiritLeadRevival.Utilities.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\images\carouselItems\" />
    <Folder Include="wwwroot\images\ministryHelpLinks\" />
    <Folder Include="wwwroot\images\sponsors\" />
    <Folder Include="wwwroot\images\staffMembers\" />
    <Folder Include="wwwroot\images\roadChaplains\" />
    <Folder Include="wwwroot\images\Pastor\" />
    <Folder Include="wwwroot\images\staff\" />
    <Folder Include="wwwroot\RoadChaplainIntroduction\" />
    <Folder Include="wwwroot\videos\" />
  </ItemGroup>

  <ItemGroup>
    <AdditionalFiles Include="Areas\Admin\Views\PastorInformation\Delete.cshtml" />
    <AdditionalFiles Include="Areas\Admin\Views\PastorInformation\Index.cshtml" />
    <AdditionalFiles Include="Areas\Admin\Views\PastorInformation\Upcert.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <UpToDateCheckInput Remove="Areas\Admin\Views\MailingList\Unsubscribed.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="Areas\Admin\Views\MailingList\Unsubscribed.cshtml" />
  </ItemGroup>

</Project>
