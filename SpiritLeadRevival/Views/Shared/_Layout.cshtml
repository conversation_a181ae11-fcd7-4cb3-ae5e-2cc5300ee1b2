@using Microsoft.AspNetCore.Mvc.TagHelpers
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="google-site-verification" content="8IJghGAaujkL--koL5uN8uEqCUlMsfMJjpA6EzQroiw" />
    <title>@ViewData["Title"] - SpiritLeadRevival</title>
    <!-- Modern CSS -->
    <link href="/dist/css/styles.css" rel="stylesheet" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
    
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <link rel="stylesheet" href="~/css/datetimepicker.css" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
    @await RenderSectionAsync("SEO", false)
    <meta property="og:image" content="http://spiritleadrevival.org/images/logo/spiritleadlogo.png" />
    <meta property="og:image:alt" content="SpiritLead Revival Ministry" />
    <meta property="og:url" content="https://spiritleadrevival.org/" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="SpiritLead Revival Ministries" />
    <meta property="og:description" content="We are a minstiry that is spiritually invested in the health and wellbeing of peoples walk with the Lord as well as their Salvation." />
</head>
<body style="background-color: #eee6d3;">
    <partial name="_svgPartial" />
    <header class="site-header">
        <div class="px-3 py-2">
            <div class="container">
                <div class="d-flex flex-wrap align-items-center justify-content-center justify-content-lg-start">
                    <a asp-area="User" asp-controller="Home" asp-action="Index" class="d-flex align-items-center my-2 my-lg-0 me-lg-auto text-white text-decoration-none">
                        <img class="logo p-3" src="/images/logo/spiritleadlogo.png" alt="SpiritLead Revival Logo">
                    </a>

                    <partial name="_NavigationPartial" />
                </div>
            </div>
        </div>
    </header>
    <div class="container">
        <main role="main" class="">
            @RenderBody()
        </main>
    </div>
    <footer class="site-footer d-flex flex-wrap justify-content-between align-items-center p-3">
        <p class="col-md-4 mb-0 text-muted">© @(DateTime.Now.Year.ToString()) SpiritLead Revival Ministries</p>

        <a href="/" class="col-md-4 d-flex align-items-center justify-content-center mb-3 mb-md-0 me-md-auto link-dark text-decoration-none">
            @* <img src="~/images/psychArmorLogo/PsychArmorCertification.png" style="max-height: 100px" alt="Psyche armor logo" /> *@
        </a>

        <ul class="nav col-md-4 justify-content-end">
            <li class="nav-item"><a asp-area="User" asp-controller="Home" asp-action="Index" class="nav-link px-2 text-muted">Home</a></li>
            <li class="nav-item"><a asp-area="User" asp-controller="Services" asp-action="Index" class="nav-link px-2 text-muted">Services</a></li>
            <li class="nav-item"><a asp-area="User" asp-controller="WaysToGive" asp-action="Index" class="nav-link px-2 text-muted">Ways to Give</a></li>
        </ul>
    </footer>
    <!-- Modern JavaScript -->
    <script src="/dist/js/main.js" asp-append-version="true"></script>
    <script src="https://kit.fontawesome.com/5d06f56e23.js" crossorigin="anonymous"></script>
    <script src="~/js/datetimepickerjquery.js"></script>
    <script src="~/js/tinymce/tinymce.min.js"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
