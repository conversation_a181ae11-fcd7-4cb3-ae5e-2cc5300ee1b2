@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Mvc.TagHelpers
@inject IAuthorizationService AuthorizationService

@if ((await AuthorizationService.AuthorizeAsync(User, "AdministratorRights")).Succeeded)
{
    <li><a class="dropdown-item" asp-area="Identity" asp-page="/Account/Register">Register User</a></li>
    <li><hr class="dropdown-divider"/></li>
    <li><a class="dropdown-item" asp-area="Admin" asp-controller="Event" asp-action="Index">Events</a></li>
    <li><hr class="dropdown-divider"/></li>
    <li><a class="dropdown-item" asp-area="Admin" asp-controller="MailingList" asp-action="Index">Mailing List</a></li>
    <li><a class="dropdown-item" asp-area="Admin" asp-controller="MailingList" asp-action="Send">Send Mass Email</a></li>
    <li><hr class="dropdown-divider"/></li>
    <li>
        <a class="dropdown-item"><i class="bi bi-chevron-double-left"></i> Home Actions</a>
        <ul class="submenu submenu-left dropdown-menu">
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="NewsItem" asp-action="Index">News</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="CarouselItem" asp-action="Index">Carousel</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="DailyScripture" asp-action="Index">Daily Scripture</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="VideoFile" asp-action="Index">Videos</a></li>
        </ul>
    </li>
    <li><hr class="dropdown-divider"/></li>
    <li>
        <a class="dropdown-item"><i class="bi bi-chevron-double-left"></i> Services Area Actions</a>
        <ul class="submenu submenu-left dropdown-menu">
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="MinistryDescription" asp-action="Index">Ministry Description</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="MinistryHelpLink" asp-action="Index">Ministry Help Links</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="PrayerRequestRecipient" asp-action="Index">Prayer Request Recipients</a></li>
        </ul>
    </li>
    <li><hr class="dropdown-divider"/></li>
    <li>
        <a class="dropdown-item"><i class="bi bi-chevron-double-left"></i> Revival Actions</a>
        <ul class="submenu submenu-left dropdown-menu">
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="RevivalConference" asp-action="Index">Conferences</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="RevivalReference" asp-action="Index">References</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="RevivalPrincipalOfFaith" asp-action="Index">Principals of Faith</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="RevivalSpiritLeadDecision" asp-action="Index">SpiritLead Decisions</a></li>
        </ul>
    </li>
    <li><hr class="dropdown-divider"/></li>
    <li>
        <a class="dropdown-item"><i class="bi bi-chevron-double-left"></i> Resources Actions</a>
        <ul class="submenu submenu-left dropdown-menu">
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="PastorInformation" asp-action="Index">Pastors</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="BoardMember" asp-action="Index">Board Members</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="StaffMember" asp-action="Index">Staff Members</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="Sponsor" asp-action="Index">Sponsors</a></li>
        </ul>
    </li>
    <li><hr class="dropdown-divider"/></li>
}

@if ((await AuthorizationService.AuthorizeAsync(User, "PrayerTeam")).Succeeded)
{
    <li>
        <a class="dropdown-item"><i class="bi bi-chevron-double-left"></i> Prayer Team Actions</a>
        <ul class="submenu submenu-left dropdown-menu">
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="PraiseReport" asp-action="Index">Praise Reports</a></li>
        </ul>
    </li>
    <li><hr class="dropdown-divider" /></li>
}