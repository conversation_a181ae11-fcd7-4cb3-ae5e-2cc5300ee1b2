@using Microsoft.AspNetCore.Mvc.TagHelpers
<ul class="nav col-12 col-lg-auto my-2 justify-content-center my-md-0 text-small">
    <li>
        <div class="dropdown-hover-css dropdown">
            <a asp-area="User" asp-controller="Home" asp-action="Index" class="nav-link text-dark">
                <svg class="bi d-block mx-auto mb-1" width="24" height="24"><use xlink:href="#home"></use></svg>
                Home
            </a>
        </div>
    </li>
    <li>
        <div class="dropdown-hover-css dropdown">
            <a href="#" class="btn dropdown-toggle text-dark" type="button" id="dropdownMenuResourcesButton" data-bs-toggle="dropdown" aria-expanded="false">
                <svg class="bi d-block mx-auto mb-1" width="24" height="24"><use xlink:href="#speedometer2"></use></svg>
                Resources
            </a>
            <ul class="dropdown-menu dropdown-menu-macos mx-0 shadow" aria-labelledby="dropdownMenuResourcesButton">
                <li><a class="dropdown-item" asp-area="User" asp-controller="Resource" asp-action="AboutUs">About Us</a></li>
                <li><a class="dropdown-item" asp-area="User" asp-controller="Resource" asp-action="ChurchNews">Church News</a></li>
                <li><a class="dropdown-item" asp-area="User" asp-controller="Resource" asp-action="ContactUs">Contact Us</a></li>
                <li><a class="dropdown-item" asp-area="User" asp-controller="Resource" asp-action="ReadingList">Reading List</a></li>
                <li><a class="dropdown-item" asp-area="User" asp-controller="Resource" asp-action="Sponsors">Sponsors</a></li>
                @* <li><a class="dropdown-item" asp-area="User" asp-controller="Resource" asp-action="VideoLibrary">Video Library</a></li> *@
                <li><a class="dropdown-item" href="https://www.youtube.com/@@SpiritLeadRevival" target="_blank" rel="noreferrer noopener">Video Library</a></li>
                <li><a class="dropdown-item" asp-area="User" asp-controller="Resource" asp-action="Vision">Vision</a></li>
            </ul>
        </div>
    </li>

    <li>
        <div class="dropdown-hover-css dropdown">
            <button href="#" class="btn dropdown-toggle text-dark" type="button" id="dropdownMenuServicesButton" data-bs-toggle="dropdown" aria-expanded="false">
                <svg class="bi d-block mx-auto mb-1" width="24" height="24"><use xlink:href="#table"></use></svg>
                Services
            </button>
            <ul class="dropdown-menu dropdown-menu-macos mx-0 shadow" @*style="width: 220px;"*@ aria-labelledby="dropdownMenuServicesButton">
                <li><a class="dropdown-item" asp-area="User" asp-controller="Services" asp-action="Index">Ministries</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" asp-area="User" asp-controller="Services" asp-action="PrayerRequest">Prayer Corner</a></li>
                <li><a class="dropdown-item" asp-area="User" asp-controller="Services" asp-action="HelpLinks">Links for help</a></li>
                <li><a class="dropdown-item" asp-area="User" asp-controller="Services" asp-action="Revivals">Revivals</a></li>
            </ul>
        </div>
    </li>
    <li>
        <div class="dropdown-hover-css dropdown">
            <a href="#" class="btn dropdown-toggle text-dark" type="button" id="dropdownMenuRoadChaplainButton" data-bs-toggle="dropdown" aria-expanded="false">
                <span class="h4"><i class="fa-solid fa-truck"></i></span><br/>
                Chaplains
            </a>
            <ul class="dropdown-menu dropdown-menu-macos mx-0 shadow" aria-labelledby="dropdownMenuResourcesButton">
                <li><a class="dropdown-item" asp-area="User" asp-controller="Chaplain" asp-action="Chaplains">Chaplain</a></li>
                <li><a class="dropdown-item" asp-area="User" asp-controller="Chaplain" asp-action="ChaplainNews">Chaplain News</a></li>
                <li><a class="dropdown-item" asp-area="User" asp-controller="Chaplain" asp-action="BecomeAChaplain">Become a Chaplain</a></li>
            </ul>
        </div>
    </li>
    <li>
        <a asp-area="User" asp-controller="WaysToGive" asp-action="Index" class="nav-link text-dark">
            <svg class="bi d-block mx-auto mb-1" width="24" height="24"><use xlink:href="#grid"></use></svg>
            Ways to Give
        </a>
    </li>
    <li>
        <div class="dropdown-hover">
            <button href="#" class="btn dropdown-toggle text-dark" type="button" id="dropdownMenuAdminButton" data-bs-toggle="dropdown" aria-expanded="false">
                <svg class="bi d-block mx-auto mb-1" width="24" height="24"><use xlink:href="#people-circle"></use></svg>
                Actions
            </button>
            <ul class="dropdown-menu dropdown-menu-macos mx-0 shadow" style="width: 220px;" aria-labelledby="dropdownMenuAdminButton">
                <partial name="_LoginPartialDropdown" />
            </ul>
        </div>
    </li>
</ul>