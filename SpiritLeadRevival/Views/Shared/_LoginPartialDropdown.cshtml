@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Mvc.TagHelpers

@inject SignInManager<IdentityUser> SignInManager
@inject UserManager<IdentityUser> UserManager
    @if (SignInManager.IsSignedIn(User))
{
    <li class="dropdown-header">User Options</li>
    <li class="dropdown-item"><a id="manage" class="nav-link text-dark" asp-area="Identity" asp-page="/Account/Manage/Index" title="Manage">Hello @UserManager.GetUserName(User)</a></li>
    <li class="dropdown-item">
        <form id="logoutForm" class="form-inline" asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
            <button id="logout" type="submit" class="nav-link btn btn-link text-dark">Logout</button>
        </form>
    </li>
    <li><hr class="dropdown-divider"></li>
    <partial name="_PartialAdminDropdown"/>
    <partial name="_PartialRoadChaplainDropdown"/>
}
else
{
    <li class="dropdown-header">Login Options</li>
    <li><a class="dropdown-item" class="text-dark" id="register" asp-area="Identity" asp-page="/Account/Register">Register</a></li>
    <li><a class="dropdown-item" class="text-dark" id="login" asp-area="Identity" asp-page="/Account/Login">Login</a></li>
    <li><hr class="dropdown-divider"></li>
}