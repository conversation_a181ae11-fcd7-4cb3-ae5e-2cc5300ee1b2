@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Mvc.TagHelpers
@inject IAuthorizationService AuthorizationService

@if ((await AuthorizationService.AuthorizeAsync(User, "ElevatedRights")).Succeeded) {
    <li>
        <a class="dropdown-item"><i class="bi bi-chevron-double-left"></i> Chaplain Actions</a>
        <ul class="submenu submenu-left dropdown-menu ">
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="Chaplain" asp-action="Index">Chaplains</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="ChaplainNews" asp-action="Index">Chaplains News</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="ChaplainIntroductionVideo" asp-action="Index">Chaplain Intro Videos</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="ChaplainIntroduction" asp-action="Index">Chaplain Introduction</a></li>
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="ChaplainVideo" asp-action="Index">Chaplain Videos</a></li>
        </ul>
    </li>
}