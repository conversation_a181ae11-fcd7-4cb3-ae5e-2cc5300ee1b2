<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Admin Panel | SpiritLead Revival</title>
    
    <!-- Bootstrap 5.3.3 from CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <!-- Custom styles that work without build process -->
    <link rel="stylesheet" href="~/css/modern-site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/modern-admin.css" asp-append-version="true" />
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <nav class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <i class="bi bi-shield-check"></i>
                </div>
                <div class="sidebar-brand-text">Admin Panel</div>
            </div>
            
            <ul class="sidebar-nav">
                <li class="nav-item">
                    <a class="nav-link" asp-area="User" asp-controller="Home" asp-action="Index">
                        <i class="nav-link-icon bi bi-house"></i>
                        <span class="nav-link-text">Back to Site</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" asp-area="Admin" asp-controller="Dashboard" asp-action="Index">
                        <i class="nav-link-icon bi bi-speedometer2"></i>
                        <span class="nav-link-text">Dashboard</span>
                    </a>
                </li>
                
                @if ((await AuthorizationService.AuthorizeAsync(User, "AdministratorRights")).Succeeded)
                {
                    <li class="nav-item nav-item-submenu">
                        <a class="nav-link" href="#">
                            <i class="nav-link-icon bi bi-newspaper"></i>
                            <span class="nav-link-text">Content Management</span>
                            <i class="bi bi-chevron-down ms-auto"></i>
                        </a>
                        <ul class="nav-submenu">
                            <li><a class="nav-link" asp-area="Admin" asp-controller="NewsItem" asp-action="Index">News Items</a></li>
                            <li><a class="nav-link" asp-area="Admin" asp-controller="CarouselItem" asp-action="Index">Carousel</a></li>
                            <li><a class="nav-link" asp-area="Admin" asp-controller="DailyScripture" asp-action="Index">Daily Scripture</a></li>
                            <li><a class="nav-link" asp-area="Admin" asp-controller="VideoFile" asp-action="Index">Videos</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item nav-item-submenu">
                        <a class="nav-link" href="#">
                            <i class="nav-link-icon bi bi-people"></i>
                            <span class="nav-link-text">People Management</span>
                            <i class="bi bi-chevron-down ms-auto"></i>
                        </a>
                        <ul class="nav-submenu">
                            <li><a class="nav-link" asp-area="Admin" asp-controller="PastorInformation" asp-action="Index">Pastors</a></li>
                            <li><a class="nav-link" asp-area="Admin" asp-controller="BoardMember" asp-action="Index">Board Members</a></li>
                            <li><a class="nav-link" asp-area="Admin" asp-controller="StaffMember" asp-action="Index">Staff Members</a></li>
                            <li><a class="nav-link" asp-area="Admin" asp-controller="Chaplain" asp-action="Index">Chaplains</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item nav-item-submenu">
                        <a class="nav-link" href="#">
                            <i class="nav-link-icon bi bi-gear"></i>
                            <span class="nav-link-text">Services</span>
                            <i class="bi bi-chevron-down ms-auto"></i>
                        </a>
                        <ul class="nav-submenu">
                            <li><a class="nav-link" asp-area="Admin" asp-controller="MinistryDescription" asp-action="Index">Ministry Description</a></li>
                            <li><a class="nav-link" asp-area="Admin" asp-controller="MinistryHelpLink" asp-action="Index">Help Links</a></li>
                            <li><a class="nav-link" asp-area="Admin" asp-controller="PrayerRequestRecipient" asp-action="Index">Prayer Recipients</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item nav-item-submenu">
                        <a class="nav-link" href="#">
                            <i class="nav-link-icon bi bi-calendar-event"></i>
                            <span class="nav-link-text">Events & Revivals</span>
                            <i class="bi bi-chevron-down ms-auto"></i>
                        </a>
                        <ul class="nav-submenu">
                            <li><a class="nav-link" asp-area="Admin" asp-controller="Event" asp-action="Index">Events</a></li>
                            <li><a class="nav-link" asp-area="Admin" asp-controller="RevivalConference" asp-action="Index">Conferences</a></li>
                            <li><a class="nav-link" asp-area="Admin" asp-controller="RevivalReference" asp-action="Index">References</a></li>
                            <li><a class="nav-link" asp-area="Admin" asp-controller="RevivalPrincipalOfFaith" asp-action="Index">Principals of Faith</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" asp-area="Admin" asp-controller="Sponsor" asp-action="Index">
                            <i class="nav-link-icon bi bi-building"></i>
                            <span class="nav-link-text">Sponsors</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" asp-area="Admin" asp-controller="MailingList" asp-action="Index">
                            <i class="nav-link-icon bi bi-envelope"></i>
                            <span class="nav-link-text">Mailing List</span>
                        </a>
                    </li>
                }
                
                @if ((await AuthorizationService.AuthorizeAsync(User, "PrayerTeam")).Succeeded)
                {
                    <li class="nav-item">
                        <a class="nav-link" asp-area="Admin" asp-controller="PraiseReport" asp-action="Index">
                            <i class="nav-link-icon bi bi-heart"></i>
                            <span class="nav-link-text">Praise Reports</span>
                        </a>
                    </li>
                }
                
                <li class="nav-item mt-auto">
                    <a class="nav-link" asp-area="Identity" asp-page="/Account/Logout">
                        <i class="nav-link-icon bi bi-box-arrow-right"></i>
                        <span class="nav-link-text">Logout</span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <!-- Main Content -->
        <div class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <button class="sidebar-toggle" type="button">
                    <i class="bi bi-list"></i>
                </button>
                
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="#" asp-area="Admin" asp-controller="Dashboard" asp-action="Index">Admin</a></li>
                        @if (ViewData["BreadcrumbItems"] != null)
                        {
                            @foreach (var item in (List<dynamic>)ViewData["BreadcrumbItems"])
                            {
                                if (item.IsActive)
                                {
                                    <li class="breadcrumb-item active" aria-current="page">@item.Text</li>
                                }
                                else
                                {
                                    <li class="breadcrumb-item"><a href="@item.Url">@item.Text</a></li>
                                }
                            }
                        }
                        else
                        {
                            <li class="breadcrumb-item active" aria-current="page">@ViewData["Title"]</li>
                        }
                    </ol>
                </nav>
                
                <div class="ms-auto">
                    <span class="text-muted">Welcome, @User.Identity.Name</span>
                </div>
            </header>
            
            <!-- Content -->
            <main class="admin-content">
                @if (TempData["success"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle me-2"></i>
                        @TempData["success"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                }
                
                @if (TempData["error"] != null)
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        @TempData["error"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                }
                
                @RenderBody()
            </main>
        </div>
    </div>
    
    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobileOverlay"></div>
    
    <!-- Scripts -->
    <!-- Bootstrap 5.3.3 JS from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <!-- jQuery for legacy compatibility -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <!-- SweetAlert2 for modern notifications -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Custom scripts that work without build process -->
    <script src="~/js/modern-site.js" asp-append-version="true"></script>
    <script src="~/js/modern-admin.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
