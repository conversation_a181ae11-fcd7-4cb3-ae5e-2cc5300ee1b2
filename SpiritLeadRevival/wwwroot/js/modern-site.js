// Modern Site JavaScript - WebDAV Compatible
// No build process required - works directly in browser

(function() {
    'use strict';

    // Modern JavaScript utilities
    class SpiritLeadApp {
        constructor() {
            this.init();
        }

        init() {
            this.setupEventListeners();
            this.initializeComponents();
            this.setupFormValidation();
            this.setupImageLazyLoading();
        }

        setupEventListeners() {
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', (e) => {
                    e.preventDefault();
                    const target = document.querySelector(anchor.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Enhanced dropdown hover effects
            document.querySelectorAll('.dropdown-hover-css').forEach(dropdown => {
                let hoverTimeout;
                
                dropdown.addEventListener('mouseenter', () => {
                    clearTimeout(hoverTimeout);
                    const menu = dropdown.querySelector('.dropdown-menu');
                    if (menu) {
                        menu.classList.add('show');
                    }
                });
                
                dropdown.addEventListener('mouseleave', () => {
                    hoverTimeout = setTimeout(() => {
                        const menu = dropdown.querySelector('.dropdown-menu');
                        if (menu) {
                            menu.classList.remove('show');
                        }
                    }, 150);
                });
            });

            // Form submission with loading states
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', (e) => {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn && !submitBtn.disabled) {
                        this.setLoadingState(submitBtn, true);
                    }
                });
            });
        }

        initializeComponents() {
            // Initialize tooltips if Bootstrap is available
            if (typeof bootstrap !== 'undefined') {
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(tooltipTriggerEl => {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });

                // Initialize popovers
                const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
                popoverTriggerList.map(popoverTriggerEl => {
                    return new bootstrap.Popover(popoverTriggerEl);
                });
            }

            // Initialize modals with enhanced animations
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('show.bs.modal', () => {
                    modal.style.display = 'block';
                    modal.classList.add('fade-in');
                });
                
                modal.addEventListener('hidden.bs.modal', () => {
                    modal.classList.remove('fade-in');
                });
            });
        }

        setupFormValidation() {
            // Enhanced form validation
            document.querySelectorAll('.needs-validation').forEach(form => {
                form.addEventListener('submit', (event) => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                        
                        // Focus on first invalid field
                        const firstInvalid = form.querySelector(':invalid');
                        if (firstInvalid) {
                            firstInvalid.focus();
                        }
                    }
                    form.classList.add('was-validated');
                });
            });

            // Real-time validation feedback
            document.querySelectorAll('input, textarea, select').forEach(field => {
                field.addEventListener('blur', () => {
                    if (field.checkValidity()) {
                        field.classList.remove('is-invalid');
                        field.classList.add('is-valid');
                    } else {
                        field.classList.remove('is-valid');
                        field.classList.add('is-invalid');
                    }
                });
            });
        }

        setupImageLazyLoading() {
            // Lazy loading for images
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });

                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }
        }

        setLoadingState(button, isLoading) {
            if (isLoading) {
                button.disabled = true;
                button.innerHTML = `
                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    Loading...
                `;
            } else {
                button.disabled = false;
                // Restore original text (you might want to store this in a data attribute)
                button.innerHTML = button.dataset.originalText || 'Submit';
            }
        }

        showNotification(message, type = 'success') {
            if (typeof Swal !== 'undefined') {
                const Toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.addEventListener('mouseenter', Swal.stopTimer);
                        toast.addEventListener('mouseleave', Swal.resumeTimer);
                    }
                });

                Toast.fire({
                    icon: type,
                    title: message
                });
            } else {
                // Fallback to alert if SweetAlert2 is not available
                alert(message);
            }
        }

        confirmDelete(element) {
            if (typeof Swal !== 'undefined') {
                return Swal.fire({
                    title: 'Are you sure?',
                    text: "You won't be able to revert this!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, delete it!',
                    cancelButtonText: 'Cancel'
                });
            } else {
                // Fallback to confirm if SweetAlert2 is not available
                return Promise.resolve({ isConfirmed: confirm('Are you sure you want to delete this item?') });
            }
        }
    }

    // File upload utilities
    class FileUploadManager {
        constructor() {
            this.setupFileUploads();
        }

        setupFileUploads() {
            document.querySelectorAll('input[type="file"]').forEach(input => {
                input.addEventListener('change', (e) => {
                    this.handleFileSelect(e.target);
                });
            });
        }

        handleFileSelect(input) {
            const files = input.files;
            const preview = input.parentElement.querySelector('.file-preview');
            
            if (preview) {
                preview.innerHTML = '';
                
                Array.from(files).forEach(file => {
                    if (file.type.startsWith('image/')) {
                        this.createImagePreview(file, preview);
                    } else {
                        this.createFilePreview(file, preview);
                    }
                });
            }
        }

        createImagePreview(file, container) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.className = 'img-thumbnail me-2 mb-2';
                img.style.maxWidth = '150px';
                img.style.maxHeight = '150px';
                container.appendChild(img);
            };
            reader.readAsDataURL(file);
        }

        createFilePreview(file, container) {
            const fileDiv = document.createElement('div');
            fileDiv.className = 'file-item d-flex align-items-center mb-2';
            fileDiv.innerHTML = `
                <i class="bi bi-file-earmark me-2"></i>
                <span>${file.name}</span>
                <small class="text-muted ms-2">(${this.formatFileSize(file.size)})</small>
            `;
            container.appendChild(fileDiv);
        }

        formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    }

    // Initialize app when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
        window.spiritLeadApp = new SpiritLeadApp();
        window.fileUploadManager = new FileUploadManager();
    });

    // Legacy jQuery support for existing code
    if (typeof $ !== 'undefined') {
        $(document).ready(function() {
            // Existing dropdown hover functionality
            $("#nav li").hover(
                function() {
                    $(this).children('ul').hide();
                    $(this).children('ul').slideDown('fast');
                },
                function() {
                    $('ul', this).slideUp('fast');
                }
            );

            $('.dropdown-hoverjs').hover(
                function() {
                    $(this).addClass('open');
                },
                function() {
                    $(this).removeClass('open');
                }
            );
        });
    }

    // Global functions for backward compatibility
    window.showNotification = function(message, type) {
        if (window.spiritLeadApp) {
            window.spiritLeadApp.showNotification(message, type);
        }
    };

    window.confirmDelete = function(element) {
        if (window.spiritLeadApp) {
            return window.spiritLeadApp.confirmDelete(element);
        }
        return Promise.resolve({ isConfirmed: confirm('Are you sure?') });
    };

})();
