// Write your Javascript code.
function uploadFiles(inputId) {
    var input = document.getElementById(inputId);
    var files = input.files;
    var formData = new FormData();

    for (var i = 0; i !== files.length; i++) {
        formData.append("files", files[i]);
    }

    startUpdatingProgressIndicator();
    $.ajax(
        {
            url: "/Upload",
            data: formData,
            processData: false,
            contentType: false,
            type: "POST",
            xhr: function () {
                var xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener("progress", function (evt) {
                    if (evt.lengthComputable) {
                        var progress = Math.round((evt.loaded / evt.total) * 100);
                        $(".progress-bar").css("width", progress + "%").attr("aria-valuenow", progress);
                        $(".progress-bar").html(progress + "%");
                    }
                }, false);
                return xhr;
            },
            success: function (data) {
                $("#progress").hide();
                $("#upload-status").show();
            }
        }
    );
}

var intervalId;

function startUpdatingProgressIndicator() {
    $("#progress").show();
    $("#upload-status").hide();

    intervalId = setInterval(
        function () {
            $.post(
                "/Admin/VideoFile/Progress",
                function (progress) {
                    $(".progress-bar").css("width", progress + "%").attr("aria-valuenow", progress);
                    $(".progress-bar").html(progress + "%");
                }
            );
        },
        1000
    );
}

$(document).ready(function () {
    $("#nav li").hover(
        function () {
            $(this).children('ul').hide();
            $(this).children('ul').slideDown('fast');
        },
        function () {
            $('ul', this).slideUp('fast');
        });
});

$(function () {
    $('.dropdown-hoverjs').hover(function () {
            $(this).addClass('open');
        },
        function () {
            $(this).removeClass('open');
        });
});
