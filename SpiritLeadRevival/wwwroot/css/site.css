.dropdown-hover-css:hover .dropdown-menu {
    display: block;
    margin-top: 0;
}

html {
    font-size: 14px;
}

@media (min-width: 768px) {
    html {
        font-size: 16px;
    }
}

div.img-wrapper {
    height: 200px;
    max-height: 300px;
    line-height: 200px;
    overflow: hidden;
    text-align: center;
    width: 200px;
    max-width: 300px;
}

div.wrapper img {
    margin: -100%;
}

html {
    position: relative;
    min-height: 100%;
}

body {
    margin-bottom: 60px;
}

.accordion .accordion-item.active {
    z-index: 2;
}

.accordion .accordion-item {
    background-color: #eee6d3;
    margin-bottom: 10px;
    position: relative;
    border-radius: 15px;
    overflow: hidden;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}

.dropdown-menu-macos {
    display: grid;
    gap: .25rem;
    padding: .5rem;
    border-radius: .5rem;
}

.dropdown-item.active, .dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #0d6efd;
}

.dropdown-menu-macos .dropdown-item {
    border-radius: .25rem;
}

.dropdown-menu {
    position: absolute;
    z-index: 1000;
    display: none;
    min-width: 10rem;
    padding: .5rem 0;
    margin: 0;
    font-size: 1rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: .25rem;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: .25rem 1rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}

div.scroll {
    max-height: 300px;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 20px;
}

div.scroll-praise-reports {
    max-height: 40rem;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 20px;
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
    width: 12px;
}

*::-webkit-scrollbar-track {
    background: #eee6d3;
    border-radius: 20px;
}

*::-webkit-scrollbar-thumb {
    background-color: #212529;
    border-radius: 20px;
    border: 3px solid #eee6d3;
}

/* ============ desktop view ============ */
@media all and (min-width: 992px) {
    .dropdown-menu li {
        position: relative;
    }

    .submenu {
        display: none;
        position: absolute;
        left: 100%;
        top: -7px;
    }

    .submenu-left {
        right: 100%;
        left: auto;
    }

    .dropdown-menu > li:hover {
        background-color: #f1f1f1
    }

        .dropdown-menu > li:hover > .submenu {
            display: block;
        }
}
/* ============ desktop view .end// ============ */

/* ============ small devices ============ */
@media (max-width: 991px) {
    .dropdown-menu .dropdown-menu {
        margin-left: 0.7rem;
        margin-right: 0.7rem;
        margin-bottom: .5rem;
    }
}
/* ============ small devices .end// ============ */
