/* Modern Admin Styles - WebDAV Compatible */

/* Admin Layout Variables */
:root {
  --admin-sidebar-width: 280px;
  --admin-sidebar-width-collapsed: 80px;
  --admin-header-height: 70px;
  --admin-mobile-breakpoint: 768px;
}

/* Admin Layout */
.admin-layout {
  display: flex;
  min-height: 100vh;
}

.admin-sidebar {
  width: var(--admin-sidebar-width);
  background: linear-gradient(180deg, var(--slr-dark) 0%, #1a1a1a 100%);
  color: var(--slr-text-white);
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  overflow-y: auto;
  transition: all 0.3s ease-in-out;
  z-index: 1000;
}

.admin-sidebar.collapsed {
  width: var(--admin-sidebar-width-collapsed);
}

.admin-sidebar.collapsed .sidebar-brand-text,
.admin-sidebar.collapsed .nav-link-text {
  opacity: 0;
  visibility: hidden;
}

.admin-sidebar.collapsed .nav-link {
  justify-content: center;
}

@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
  }
  
  .admin-sidebar.show {
    transform: translateX(0);
  }
}

.admin-main {
  flex: 1;
  margin-left: var(--admin-sidebar-width);
  transition: all 0.3s ease-in-out;
}

.admin-sidebar.collapsed + .admin-main {
  margin-left: var(--admin-sidebar-width-collapsed);
}

@media (max-width: 768px) {
  .admin-main {
    margin-left: 0;
  }
}

/* Admin Header */
.admin-header {
  background: var(--slr-bg-secondary);
  height: var(--admin-header-height);
  display: flex;
  align-items: center;
  padding: 0 1.5rem;
  box-shadow: var(--slr-shadow-sm);
  position: sticky;
  top: 0;
  z-index: 999;
}

.sidebar-toggle {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--slr-text-primary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--slr-border-radius);
  transition: var(--slr-transition);
}

.sidebar-toggle:hover {
  background-color: var(--slr-bg-tertiary);
}

@media (min-width: 769px) {
  .sidebar-toggle {
    display: none;
  }
}

.breadcrumb {
  background: none;
  padding: 0;
  margin: 0 0 0 1rem;
}

.breadcrumb-item.active {
  color: var(--slr-primary);
  font-weight: 600;
}

/* Sidebar Navigation */
.sidebar-brand {
  padding: 1.5rem;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-brand-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.sidebar-brand-text {
  font-size: 1.25rem;
  font-weight: 700;
  transition: var(--slr-transition);
}

.sidebar-nav {
  padding: 1rem 0;
}

.sidebar-nav .nav-item {
  margin-bottom: 0.5rem;
}

.sidebar-nav .nav-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: var(--slr-transition);
  border-radius: 0;
}

.sidebar-nav .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--slr-text-white);
}

.sidebar-nav .nav-link.active {
  background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
  color: var(--slr-text-white);
}

.nav-link-icon {
  width: 1.5rem;
  text-align: center;
  margin-right: 1rem;
  transition: var(--slr-transition);
}

.nav-link-text {
  transition: var(--slr-transition);
}

/* Submenu styles */
.nav-submenu {
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  background-color: rgba(0, 0, 0, 0.2);
}

.nav-submenu.show {
  max-height: 500px;
}

.nav-submenu .nav-link {
  padding-left: calc(1.5rem + 2rem);
  font-size: 0.9rem;
}

/* Admin Content */
.admin-content {
  padding: 1.5rem;
}

@media (max-width: 768px) {
  .admin-content {
    padding: 1rem;
  }
}

/* Admin Cards */
.admin-card {
  background: var(--slr-bg-secondary);
  border-radius: var(--slr-border-radius-lg);
  box-shadow: var(--slr-shadow);
  margin-bottom: 1.5rem;
}

.admin-card-header {
  background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
  color: var(--slr-text-white);
  padding: 1.5rem;
  border-radius: var(--slr-border-radius-lg) var(--slr-border-radius-lg) 0 0;
}

.admin-card-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.admin-card-actions {
  display: flex;
  gap: 0.5rem;
}

.admin-card-actions .btn {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.admin-card-body {
  padding: 1.5rem;
}

/* Responsive Admin Table */
.admin-table-container {
  background: var(--slr-bg-secondary);
  border-radius: var(--slr-border-radius-lg);
  box-shadow: var(--slr-shadow);
  overflow: hidden;
}

.admin-table {
  margin: 0;
}

.admin-table thead th {
  background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
  color: var(--slr-text-white);
  border: none;
  padding: 1rem 1.5rem;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

.admin-table tbody tr {
  transition: var(--slr-transition);
}

.admin-table tbody tr:hover {
  background-color: rgba(63, 154, 214, 0.05);
}

.admin-table tbody td {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--slr-border-light);
  vertical-align: middle;
}

/* Mobile responsive table */
@media (max-width: 768px) {
  .admin-table thead {
    display: none;
  }
  
  .admin-table tbody tr {
    display: block;
    margin-bottom: 1rem;
    background: var(--slr-bg-secondary);
    border-radius: var(--slr-border-radius);
    box-shadow: var(--slr-shadow-sm);
  }
  
  .admin-table tbody td {
    display: block;
    padding: 0.5rem 1rem;
    border: none;
    border-bottom: 1px solid var(--slr-border-light);
  }
  
  .admin-table tbody td:last-child {
    border-bottom: none;
  }
  
  .admin-table tbody td:before {
    content: attr(data-label) ": ";
    font-weight: 600;
    color: var(--slr-primary);
  }
}

/* Action buttons */
.admin-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.admin-actions .btn {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-actions .btn i {
  font-size: 1rem;
}

@media (max-width: 480px) {
  .admin-actions {
    flex-direction: column;
  }
  
  .admin-actions .btn {
    justify-content: center;
  }
}

/* Mobile overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: var(--slr-transition);
}

.mobile-overlay.show {
  opacity: 1;
  visibility: visible;
}

@media (min-width: 769px) {
  .mobile-overlay {
    display: none;
  }
}

/* Form improvements for admin */
.admin-form .form-group {
  margin-bottom: 1.5rem;
}

.admin-form .form-group label {
  font-weight: 600;
  color: var(--slr-text-primary);
  margin-bottom: 0.5rem;
}

.admin-form .form-control {
  padding: 1rem 1.5rem;
}

.admin-form .form-text {
  color: var(--slr-text-secondary);
  font-size: 0.875rem;
}

/* Table search */
.table-search {
  margin-bottom: 1rem;
}

.table-search .input-group-text {
  background-color: var(--slr-bg-tertiary);
  border-color: var(--slr-border-light);
}
