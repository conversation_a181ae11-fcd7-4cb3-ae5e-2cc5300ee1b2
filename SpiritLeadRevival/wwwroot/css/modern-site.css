/* Modern Site Styles - WebDAV Compatible */

/* CSS Custom Properties for theming */
:root {
  --slr-primary: #3F9AD6;
  --slr-secondary: #eee6d3;
  --slr-accent: #1b6ec2;
  --slr-success: #28a745;
  --slr-warning: #ffc107;
  --slr-danger: #dc3545;
  --slr-info: #17a2b8;
  --slr-light: #f8f9fa;
  --slr-dark: #212529;
  
  --slr-bg-primary: #eee6d3;
  --slr-bg-secondary: #ffffff;
  --slr-bg-tertiary: #f8f9fa;
  
  --slr-text-primary: #212529;
  --slr-text-secondary: #6c757d;
  --slr-text-muted: #868e96;
  --slr-text-white: #ffffff;
  
  --slr-border-color: #dee2e6;
  --slr-border-light: #e9ecef;
  
  --slr-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --slr-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --slr-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  
  --slr-border-radius: 0.375rem;
  --slr-border-radius-lg: 0.5rem;
  --slr-border-radius-xl: 1rem;
  
  --slr-transition: all 0.15s ease-in-out;
}

/* Base styles */
body {
  background-color: var(--slr-bg-primary);
  font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Header styles */
.site-header {
  background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
  border-radius: 0 0 var(--slr-border-radius-lg) var(--slr-border-radius-lg);
  box-shadow: var(--slr-shadow);
}

.site-header .logo {
  max-width: 350px;
  height: auto;
  transition: var(--slr-transition);
}

@media (max-width: 768px) {
  .site-header .logo {
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .site-header .logo {
    max-width: 200px;
  }
}

/* Navigation improvements */
.nav-link {
  transition: var(--slr-transition);
  border-radius: var(--slr-border-radius);
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.nav-link svg {
  transition: var(--slr-transition);
}

.nav-link:hover svg {
  transform: scale(1.1);
}

/* Dropdown improvements */
.dropdown-menu {
  border: none;
  box-shadow: var(--slr-shadow-lg);
  border-radius: var(--slr-border-radius-lg);
  padding: 0.5rem;
}

.dropdown-item {
  border-radius: var(--slr-border-radius);
  transition: var(--slr-transition);
}

.dropdown-item:hover {
  background-color: var(--slr-primary);
  color: var(--slr-text-white);
  transform: translateX(4px);
}

/* Card improvements */
.card {
  border: none;
  box-shadow: var(--slr-shadow);
  border-radius: var(--slr-border-radius-lg);
  transition: var(--slr-transition);
}

.card:hover {
  box-shadow: var(--slr-shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
  color: var(--slr-text-white);
  border-radius: var(--slr-border-radius-lg) var(--slr-border-radius-lg) 0 0;
  border: none;
}

/* Button improvements */
.btn {
  border-radius: var(--slr-border-radius);
  transition: var(--slr-transition);
  font-weight: 500;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--slr-shadow);
}

.btn-primary {
  background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
  border: none;
}

.btn-success {
  background: linear-gradient(135deg, var(--slr-success) 0%, #218838 100%);
  border: none;
}

.btn-danger {
  background: linear-gradient(135deg, var(--slr-danger) 0%, #c82333 100%);
  border: none;
}

/* Form improvements */
.form-control {
  border-radius: var(--slr-border-radius);
  border: 2px solid var(--slr-border-light);
  transition: var(--slr-transition);
}

.form-control:focus {
  border-color: var(--slr-primary);
  box-shadow: 0 0 0 0.2rem rgba(63, 154, 214, 0.25);
}

/* Table improvements */
.table {
  background-color: var(--slr-bg-secondary);
  border-radius: var(--slr-border-radius-lg);
  overflow: hidden;
  box-shadow: var(--slr-shadow);
}

.table thead th {
  background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
  color: var(--slr-text-white);
  border: none;
  font-weight: 600;
}

.table tbody tr {
  transition: var(--slr-transition);
}

.table tbody tr:hover {
  background-color: rgba(63, 154, 214, 0.05);
}

/* Footer styles */
.site-footer {
  background-color: var(--slr-bg-secondary);
  border-top: 1px solid var(--slr-border-light);
  margin-top: 3rem;
}

/* Utility classes */
.shadow-custom {
  box-shadow: var(--slr-shadow) !important;
}

.shadow-custom-lg {
  box-shadow: var(--slr-shadow-lg) !important;
}

.rounded-custom {
  border-radius: var(--slr-border-radius-lg) !important;
}

.text-gradient {
  background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .site-header .container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  .table-responsive {
    border-radius: var(--slr-border-radius-lg);
  }
}

/* Loading states */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Scroll improvements */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--slr-bg-tertiary);
  border-radius: var(--slr-border-radius);
}

::-webkit-scrollbar-thumb {
  background: var(--slr-primary);
  border-radius: var(--slr-border-radius);
}

::-webkit-scrollbar-thumb:hover {
  background: #2980b9;
}

/* Legacy compatibility */
.dropdown-hover-css:hover .dropdown-menu {
  display: block;
  margin-top: 0;
}

.dropdown-menu-macos {
  display: grid;
  gap: 0.25rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.dropdown-menu-macos .dropdown-item {
  border-radius: 0.25rem;
}

/* Existing scroll styles compatibility */
div.scroll {
  max-height: 300px;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 20px;
}

div.scroll-praise-reports {
  max-height: 40rem;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 20px;
}
