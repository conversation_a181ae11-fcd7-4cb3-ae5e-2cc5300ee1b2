# Debug Launch Script for SpiritLead Revival
param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Admin", "Dashboard", "Home", "Site")]
    [string]$Target = "Admin",
    
    [Parameter(Mandatory=$false)]
    [switch]$Clean = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$NoLaunch = $false
)

Write-Host "🐛 SpiritLead Revival - Debug Launch" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""

# Check if we're in the right directory
if (!(Test-Path "SpiritLeadRevival.csproj")) {
    Write-Host "❌ Error: SpiritLeadRevival.csproj not found in current directory" -ForegroundColor Red
    Write-Host "Please run this script from the project root directory" -ForegroundColor Yellow
    exit 1
}

# Clean if requested
if ($Clean) {
    Write-Host "🧹 Cleaning previous builds..." -ForegroundColor Yellow
    try {
        dotnet clean --configuration Debug
        if ($LASTEXITCODE -ne 0) {
            throw "Clean failed"
        }
        Write-Host "✅ Clean completed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Clean failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
    Write-Host ""
}

# Restore packages
Write-Host "📦 Restoring packages..." -ForegroundColor Yellow
try {
    if ($Verbose) {
        dotnet restore --verbosity normal
    } else {
        dotnet restore --verbosity minimal
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "Restore failed"
    }
    Write-Host "✅ Package restore completed" -ForegroundColor Green
}
catch {
    Write-Host "❌ Package restore failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
Write-Host ""

# Build in Debug configuration
Write-Host "🔨 Building in Debug mode..." -ForegroundColor Yellow
try {
    if ($Verbose) {
        dotnet build --configuration Debug --verbosity normal
    } else {
        dotnet build --configuration Debug --verbosity minimal
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    Write-Host "✅ Build completed successfully" -ForegroundColor Green
}
catch {
    Write-Host "❌ Build failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please fix build errors before launching" -ForegroundColor Yellow
    exit 1
}
Write-Host ""

# Determine launch profile and URL
$LaunchProfile = ""
$LaunchUrl = ""

switch ($Target) {
    "Admin" {
        $LaunchProfile = "SpiritLeadRevival-Debug"
        $LaunchUrl = "https://localhost:7093/Admin"
    }
    "Dashboard" {
        $LaunchProfile = "Admin-Dashboard-Debug"
        $LaunchUrl = "https://localhost:7093/Admin/Dashboard"
    }
    "Home" {
        $LaunchProfile = "SpiritLeadRevival"
        $LaunchUrl = "https://localhost:7093"
    }
    "Site" {
        $LaunchProfile = "SpiritLeadRevival"
        $LaunchUrl = "https://localhost:7093"
    }
}

Write-Host "🚀 Launching application..." -ForegroundColor Yellow
Write-Host "   Profile: $LaunchProfile" -ForegroundColor Gray
Write-Host "   Target: $Target" -ForegroundColor Gray
Write-Host "   URL: $LaunchUrl" -ForegroundColor Gray
Write-Host ""

if ($NoLaunch) {
    Write-Host "🔧 Starting server without browser launch..." -ForegroundColor Yellow
    Write-Host "   Navigate manually to: $LaunchUrl" -ForegroundColor Cyan
    Write-Host "   Press Ctrl+C to stop the server" -ForegroundColor Gray
    Write-Host ""
    
    # Set environment variables for debug
    $env:ASPNETCORE_ENVIRONMENT = "Development"
    $env:ASPNETCORE_DETAILEDERRORS = "true"
    $env:ASPNETCORE_LOGGING__LOGLEVEL__DEFAULT = "Debug"
    
    # Run without launching browser
    dotnet run --configuration Debug --no-build --urls "https://localhost:7093;http://localhost:5093"
} else {
    Write-Host "🌐 Launching with browser..." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🎯 Debug Features Enabled:" -ForegroundColor Cyan
    Write-Host "   ✅ Detailed error pages" -ForegroundColor White
    Write-Host "   ✅ Debug logging" -ForegroundColor White
    Write-Host "   ✅ Hot reload enabled" -ForegroundColor White
    Write-Host "   ✅ Modern responsive UI" -ForegroundColor White
    Write-Host "   ✅ Mobile-friendly admin panel" -ForegroundColor White
    Write-Host ""
    Write-Host "📱 Test the responsive design by:" -ForegroundColor Yellow
    Write-Host "   1. Resizing your browser window" -ForegroundColor White
    Write-Host "   2. Using browser dev tools (F12)" -ForegroundColor White
    Write-Host "   3. Testing on mobile device" -ForegroundColor White
    Write-Host ""
    Write-Host "🛑 Press Ctrl+C to stop the server" -ForegroundColor Gray
    Write-Host ""
    
    # Launch with the specified profile
    dotnet run --configuration Debug --no-build --launch-profile $LaunchProfile
}

if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ Launch failed with exit code $LASTEXITCODE" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔍 Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "   1. Check if ports 7093/5093 are available" -ForegroundColor White
    Write-Host "   2. Verify SSL certificate is trusted" -ForegroundColor White
    Write-Host "   3. Try running with -Verbose for more details" -ForegroundColor White
    Write-Host "   4. Check firewall settings" -ForegroundColor White
    exit 1
}

Write-Host ""
Write-Host "👋 Debug session ended. Happy coding!" -ForegroundColor Green
