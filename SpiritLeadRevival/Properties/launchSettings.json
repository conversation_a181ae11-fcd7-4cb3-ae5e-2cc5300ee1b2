{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:21064", "sslPort": 44330}}, "profiles": {"SpiritLeadRevival": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://localhost:7093;http://localhost:5093", "dotnetRunMessages": true}, "SpiritLeadRevival-Debug": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "Admin", "applicationUrl": "https://localhost:7093;http://localhost:5093", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_DETAILEDERRORS": "true", "ASPNETCORE_LOGGING__LOGLEVEL__DEFAULT": "Debug", "ASPNETCORE_LOGGING__LOGLEVEL__MICROSOFT": "Information", "ASPNETCORE_LOGGING__LOGLEVEL__MICROSOFT.HOSTING.LIFETIME": "Information"}, "hotReloadEnabled": true, "hotReloadProfile": "aspnetcore"}, "Admin-Dashboard-Debug": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "Admin/Dashboard", "applicationUrl": "https://localhost:7093;http://localhost:5093", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_DETAILEDERRORS": "true", "ASPNETCORE_LOGGING__LOGLEVEL__DEFAULT": "Debug", "ASPNETCORE_LOGGING__LOGLEVEL__MICROSOFT": "Warning", "ASPNETCORE_LOGGING__LOGLEVEL__MICROSOFT.HOSTING.LIFETIME": "Information", "ASPNETCORE_LOGGING__LOGLEVEL__SPIRITLEADREVIVAL": "Debug"}, "hotReloadEnabled": true, "hotReloadProfile": "aspnetcore"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://localhost:5001;http://localhost:5000", "nativeDebugging": true}, "IIS Express - Admin Debug": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "Admin", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_DETAILEDERRORS": "true", "ASPNETCORE_LOGGING__LOGLEVEL__DEFAULT": "Debug"}, "nativeDebugging": true}}}