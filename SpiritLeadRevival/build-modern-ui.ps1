# PowerShell script to build modern UI assets
Write-Host "Building Modern UI for SpiritLead Revival..." -ForegroundColor Green

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Yellow
} catch {
    Write-Host "Node.js is not installed. Please install Node.js first." -ForegroundColor Red
    exit 1
}

# Install dependencies
Write-Host "Installing dependencies..." -ForegroundColor Yellow
npm install

# Build assets
Write-Host "Building assets..." -ForegroundColor Yellow
npm run build

# Create directories if they don't exist
$directories = @(
    "wwwroot/dist",
    "wwwroot/dist/css",
    "wwwroot/dist/js",
    "wwwroot/dist/assets"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "Created directory: $dir" -ForegroundColor Green
    }
}

Write-Host "Modern UI build completed!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Run the application to see the modernized interface" -ForegroundColor White
Write-Host "2. Check the admin panel for the new responsive design" -ForegroundColor White
Write-Host "3. Test on mobile devices to verify responsiveness" -ForegroundColor White
