# PowerShell script to build and deploy SpiritLead Revival to Site4Now
param(
    [Parameter(Mandatory=$false)]
    [string]$Password,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipBuild = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose = $false
)

# Configuration
$SiteName = "spiritle-001-site1"
$Username = "spiritle-001"
$ServiceUrl = "https://win8131.site4now.net:8172/MsDeploy.axd?site=spiritle-001-site1"
$PublishProfile = "spiritle-001-site1"

Write-Host "🚀 SpiritLead Revival - Automated Deployment Script" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green
Write-Host ""

# Check if we're in the right directory
if (!(Test-Path "SpiritLeadRevival.csproj")) {
    Write-Host "❌ Error: SpiritLeadRevival.csproj not found in current directory" -ForegroundColor Red
    Write-Host "Please run this script from the project root directory" -ForegroundColor Yellow
    exit 1
}

# Get password if not provided
if ([string]::IsNullOrEmpty($Password)) {
    $SecurePassword = Read-Host "Enter password for $Username" -AsSecureString
    $Password = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword))
}

Write-Host "📋 Deployment Configuration:" -ForegroundColor Cyan
Write-Host "   Site Name: $SiteName" -ForegroundColor White
Write-Host "   Username: $Username" -ForegroundColor White
Write-Host "   Service URL: $ServiceUrl" -ForegroundColor White
Write-Host ""

# Step 1: Clean previous builds
if (!$SkipBuild) {
    Write-Host "🧹 Cleaning previous builds..." -ForegroundColor Yellow
    try {
        dotnet clean --configuration Release
        if ($LASTEXITCODE -ne 0) {
            throw "Clean failed"
        }
        Write-Host "✅ Clean completed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Clean failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
    Write-Host ""
}

# Step 2: Restore packages
if (!$SkipBuild) {
    Write-Host "📦 Restoring NuGet packages..." -ForegroundColor Yellow
    try {
        dotnet restore
        if ($LASTEXITCODE -ne 0) {
            throw "Restore failed"
        }
        Write-Host "✅ Package restore completed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Package restore failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
    Write-Host ""
}

# Step 3: Build the application
if (!$SkipBuild) {
    Write-Host "🔨 Building application..." -ForegroundColor Yellow
    try {
        if ($Verbose) {
            dotnet build --configuration Release --verbosity normal
        } else {
            dotnet build --configuration Release --verbosity minimal
        }
        
        if ($LASTEXITCODE -ne 0) {
            throw "Build failed"
        }
        Write-Host "✅ Build completed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Build failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Please fix build errors before deploying" -ForegroundColor Yellow
        exit 1
    }
    Write-Host ""
}

# Step 4: Verify modern UI files exist
Write-Host "🎨 Verifying modern UI files..." -ForegroundColor Yellow
$RequiredFiles = @(
    "wwwroot\css\modern-site.css",
    "wwwroot\css\modern-admin.css", 
    "wwwroot\js\modern-site.js",
    "wwwroot\js\modern-admin.js",
    "Views\Shared\_AdminLayout.cshtml",
    "Areas\Admin\Views\_ViewStart.cshtml",
    "Areas\Admin\Controllers\DashboardController.cs",
    "Areas\Admin\Views\Dashboard\Index.cshtml"
)

$MissingFiles = @()
foreach ($file in $RequiredFiles) {
    if (!(Test-Path $file)) {
        $MissingFiles += $file
    }
}

if ($MissingFiles.Count -gt 0) {
    Write-Host "❌ Missing required modern UI files:" -ForegroundColor Red
    foreach ($file in $MissingFiles) {
        Write-Host "   - $file" -ForegroundColor Red
    }
    Write-Host "Please ensure all modern UI files are present before deploying" -ForegroundColor Yellow
    exit 1
}
Write-Host "✅ All modern UI files verified" -ForegroundColor Green
Write-Host ""

# Step 5: Deploy using Web Deploy
Write-Host "🚀 Deploying to Site4Now..." -ForegroundColor Yellow
Write-Host "   This may take a few minutes..." -ForegroundColor Gray

try {
    $DeployArgs = @(
        "publish"
        "--configuration", "Release"
        "--framework", "net8.0"
        "/p:PublishProfile=$PublishProfile"
        "/p:UserName=$Username"
        "/p:Password=$Password"
        "/p:AllowUntrustedCertificate=true"
        "/p:MSDeployServiceURL=$ServiceUrl"
        "/p:DeployDefaultTarget=WebPublishMethod"
        "/p:WebPublishMethod=MSDeploy"
        "/p:PublishUrl=$ServiceUrl"
    )
    
    if ($Verbose) {
        $DeployArgs += "/p:PublishVerbosity=Normal"
    }
    
    & dotnet @DeployArgs
    
    if ($LASTEXITCODE -ne 0) {
        throw "Deployment failed with exit code $LASTEXITCODE"
    }
    
    Write-Host ""
    Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📱 Your modernized application features:" -ForegroundColor Cyan
    Write-Host "   ✅ Responsive admin interface" -ForegroundColor White
    Write-Host "   ✅ Mobile-friendly design" -ForegroundColor White
    Write-Host "   ✅ Modern dashboard" -ForegroundColor White
    Write-Host "   ✅ Enhanced user experience" -ForegroundColor White
    Write-Host ""
    Write-Host "🌐 Visit your site: https://spiritleadrevival.org" -ForegroundColor Green
    Write-Host "🔧 Admin panel: https://spiritleadrevival.org/Admin" -ForegroundColor Green
    
}
catch {
    Write-Host ""
    Write-Host "❌ Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔍 Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "   1. Verify your username and password" -ForegroundColor White
    Write-Host "   2. Check your internet connection" -ForegroundColor White
    Write-Host "   3. Ensure Site4Now service is accessible" -ForegroundColor White
    Write-Host "   4. Try running with -Verbose flag for more details" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 Run with -Verbose for detailed output:" -ForegroundColor Cyan
    Write-Host "   .\deploy-to-site4now.ps1 -Verbose" -ForegroundColor Gray
    
    exit 1
}

Write-Host ""
Write-Host "🎯 Deployment Summary:" -ForegroundColor Cyan
Write-Host "   Status: SUCCESS ✅" -ForegroundColor Green
Write-Host "   Target: $SiteName" -ForegroundColor White
Write-Host "   Modern UI: Deployed ✅" -ForegroundColor Green
Write-Host "   Mobile Ready: Yes ✅" -ForegroundColor Green
Write-Host ""
Write-Host "Happy coding! 🚀" -ForegroundColor Green
