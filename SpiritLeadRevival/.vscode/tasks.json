{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/SpiritLeadRevival.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/SpiritLeadRevival.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/SpiritLeadRevival.csproj"], "problemMatcher": "$msCompile", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/SpiritLeadRevival.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/SpiritLeadRevival.csproj"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "debug-build-and-launch", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/debug-launch.ps1", "-Target", "Admin"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "deploy-to-site4now", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/quick-deploy.ps1"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}]}