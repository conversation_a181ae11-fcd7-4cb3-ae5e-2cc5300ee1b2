{"version": "0.2.0", "configurations": [{"name": "SpiritLead Revival - Debug", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/bin/Debug/net8.0/SpiritLeadRevival.dll", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_DETAILEDERRORS": "true", "ASPNETCORE_LOGGING__LOGLEVEL__DEFAULT": "Debug", "ASPNETCORE_LOGGING__LOGLEVEL__MICROSOFT": "Information", "ASPNETCORE_LOGGING__LOGLEVEL__MICROSOFT.HOSTING.LIFETIME": "Information"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "SpiritLead Revival - <PERSON><PERSON>", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/bin/Debug/net8.0/SpiritLeadRevival.dll", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)", "uriFormat": "%s/Admin"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_DETAILEDERRORS": "true", "ASPNETCORE_LOGGING__LOGLEVEL__DEFAULT": "Debug", "ASPNETCORE_LOGGING__LOGLEVEL__MICROSOFT": "Warning", "ASPNETCORE_LOGGING__LOGLEVEL__MICROSOFT.HOSTING.LIFETIME": "Information", "ASPNETCORE_LOGGING__LOGLEVEL__SPIRITLEADREVIVAL": "Debug"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views", "/Areas": "${workspaceFolder}/Areas"}}, {"name": "SpiritLead Revival - Dashboard Debug", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/bin/Debug/net8.0/SpiritLeadRevival.dll", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)", "uriFormat": "%s/Admin/Dashboard"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_DETAILEDERRORS": "true", "ASPNETCORE_LOGGING__LOGLEVEL__DEFAULT": "Debug", "ASPNETCORE_LOGGING__LOGLEVEL__MICROSOFT": "Warning", "ASPNETCORE_LOGGING__LOGLEVEL__MICROSOFT.HOSTING.LIFETIME": "Information", "ASPNETCORE_LOGGING__LOGLEVEL__SPIRITLEADREVIVAL": "Debug"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views", "/Areas": "${workspaceFolder}/Areas"}}]}