@echo off
echo.
echo ========================================
echo  SpiritLead Revival - Debug Launch
echo ========================================
echo.
echo Choose your debug target:
echo.
echo 1. Admin Panel (Default)
echo 2. Dashboard
echo 3. Home Page
echo 4. Server Only (No Browser)
echo.
set /p choice="Enter your choice (1-4, default=1): "

if "%choice%"=="" set choice=1
if "%choice%"=="1" goto admin
if "%choice%"=="2" goto dashboard
if "%choice%"=="3" goto home
if "%choice%"=="4" goto server
goto admin

:admin
echo.
echo Starting Admin Panel Debug...
powershell -ExecutionPolicy Bypass -File "debug-launch.ps1" -Target Admin
goto end

:dashboard
echo.
echo Starting Dashboard Debug...
powershell -ExecutionPolicy Bypass -File "debug-launch.ps1" -Target Dashboard
goto end

:home
echo.
echo Starting Home Page Debug...
powershell -ExecutionPolicy Bypass -File "debug-launch.ps1" -Target Home
goto end

:server
echo.
echo Starting Server Only (No Browser)...
powershell -ExecutionPolicy Bypass -File "debug-launch.ps1" -Target Admin -NoLaunch
goto end

:end
echo.
echo Press any key to exit...
pause >nul
