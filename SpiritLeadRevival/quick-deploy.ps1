# Quick Deploy Script for SpiritLead Revival
# This script builds and deploys in one command

Write-Host "🚀 SpiritLead Revival - Quick Deploy" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""

# Build and deploy using the publish profile
Write-Host "🔨 Building and deploying..." -ForegroundColor Yellow

try {
    # Use the publish profile directly
    dotnet publish --configuration Release --framework net8.0 --verbosity minimal /p:PublishProfile=spiritle-001-site1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "🎉 Deployment successful!" -ForegroundColor Green
        Write-Host ""
        Write-Host "📱 Your modernized features are now live:" -ForegroundColor Cyan
        Write-Host "   ✅ Responsive admin interface" -ForegroundColor White
        Write-Host "   ✅ Mobile-friendly design" -ForegroundColor White
        Write-Host "   ✅ Modern dashboard" -ForegroundColor White
        Write-Host "   ✅ Enhanced user experience" -ForegroundColor White
        Write-Host ""
        Write-Host "🌐 Visit: https://spiritleadrevival.org" -ForegroundColor Green
        Write-Host "🔧 Admin: https://spiritleadrevival.org/Admin" -ForegroundColor Green
    } else {
        throw "Deployment failed with exit code $LASTEXITCODE"
    }
}
catch {
    Write-Host ""
    Write-Host "❌ Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔍 Try these troubleshooting steps:" -ForegroundColor Yellow
    Write-Host "   1. Check internet connection" -ForegroundColor White
    Write-Host "   2. Verify Site4Now service is running" -ForegroundColor White
    Write-Host "   3. Ensure credentials are correct" -ForegroundColor White
    exit 1
}

Write-Host ""
Write-Host "✨ Deployment complete! Your modern UI is live! ✨" -ForegroundColor Green
