@echo off
echo.
echo ========================================
echo  SpiritLead Revival - Quick Deploy
echo ========================================
echo.

REM Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PowerShell is not available
    echo Please install PowerShell to use this deployment script
    pause
    exit /b 1
)

REM Run the PowerShell deployment script
echo Starting deployment...
echo.
powershell -ExecutionPolicy Bypass -File "deploy-to-site4now.ps1"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo  Deployment completed successfully!
    echo ========================================
) else (
    echo.
    echo ========================================
    echo  Deployment failed!
    echo ========================================
)

echo.
echo Press any key to exit...
pause >nul
