<configuration>
	<!-- To customize the asp.net core module uncomment and edit the following section. 
  For more info see https://go.microsoft.com/fwlink/?linkid=838655 -->
	<system.webServer>
		<handlers>
			<remove name="aspNetCore" />
			<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
			hostingModel="OutOfProcess"

		</handlers>
		<security>
			<requestFiltering>
				<requestLimits maxAllowedContentLength="524288000" />
			</requestFiltering>
		</security>
		<aspNetCore processPath="dotnet" arguments=".\SpiritLeadRevival.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="InProcess">
			<environmentVariables>
				<!--<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Development" />-->
			</environmentVariables>
		</aspNetCore>
	</system.webServer>
	<location path="videos" allowOverride="false">
		<system.webServer>
			<directoryBrowse enabled="true" showFlags="Date, Time, Size, Extension" />
			<defaultDocument>
				<files>
					<!-- When requesting a file listing, don't serve up the default 
               index.html file if it exists. -->
					<clear />
				</files>
			</defaultDocument>
			<security>
				<authorization>
					<!-- Allow all users access to the Public folder -->
					<remove users="*" roles="" verbs="" />
					<add accessType="Allow" users="*" roles="" />
				</authorization>
				<!-- Unblock all sourcecode related extensions (.cs, .aspx, .mdf)
             and files/folders (web.config, \bin) -->
				<requestFiltering>
					<hiddenSegments>
						<clear />
					</hiddenSegments>
					<fileExtensions>
						<clear />
					</fileExtensions>
				</requestFiltering>
			</security>
			<!-- Remove all ASP.NET file extension associations.
           Only include this if you have the ASP.NET feature installed, 
           otherwise this produces an Invalid configuration error. -->
			<handlers>
				<clear />
				<add name="StaticFile" path="*" verb="*" modules="StaticFileModule,DefaultDocumentModule,DirectoryListingModule" resourceType="Either" requireAccess="Read" />
			</handlers>
			<!-- Map all extensions to the same MIME type, so all files can be
           downloaded. -->
			<staticContent>
				<clear />
				<mimeMap fileExtension="*" mimeType="application/octet-stream" />
			</staticContent>
		</system.webServer>
	</location>
	<location path="images" allowOverride="false">
		<system.webServer>
			<directoryBrowse enabled="true" showFlags="Date, Time, Size, Extension" />
			<defaultDocument>
				<files>
					<!-- When requesting a file listing, don't serve up the default 
               index.html file if it exists. -->
					<clear />
				</files>
			</defaultDocument>
			<security>
				<authorization>
					<!-- Allow all users access to the Public folder -->
					<remove users="*" roles="" verbs="" />
					<add accessType="Allow" users="*" roles="" />
				</authorization>
				<!-- Unblock all sourcecode related extensions (.cs, .aspx, .mdf)
             and files/folders (web.config, \bin) -->
				<requestFiltering>
					<hiddenSegments>
						<clear />
					</hiddenSegments>
					<fileExtensions>
						<clear />
					</fileExtensions>
				</requestFiltering>
			</security>
			<!-- Remove all ASP.NET file extension associations.
           Only include this if you have the ASP.NET feature installed, 
           otherwise this produces an Invalid configuration error. -->
			<handlers>
				<clear />
				<add name="StaticFile" path="*" verb="*" modules="StaticFileModule,DefaultDocumentModule,DirectoryListingModule" resourceType="Either" requireAccess="Read" />
			</handlers>
			<!-- Map all extensions to the same MIME type, so all files can be
           downloaded. -->
			<staticContent>
				<clear />
				<mimeMap fileExtension="*" mimeType="application/octet-stream" />
			</staticContent>
		</system.webServer>
	</location>
	<system.web>
		<compilation defaultLanguage="c#" tempDirectory="f:\websites\vhosts\spiritleadrevival.org\tmp" />
	</system.web>
</configuration>
<!--ProjectGuid: 9191dada-09c4-4ae5-bda4-425e326c4336-->