using AspNetCore.SEOHelper;
using AspNetSeo.CoreMvc;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.EntityFrameworkCore;
using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Repositories.Repository;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;
using System.Net;
using IWebHostEnvironment = Microsoft.AspNetCore.Hosting.IWebHostEnvironment;

var builder = WebApplication.CreateBuilder(args);
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");


// Add services to the container.
builder.Services.Configure<FormOptions>(options => options.MultipartBodyLengthLimit = StaticDetails.FileSizeLimit500MB);
builder.Services.Configure<IISServerOptions>(options => options.MaxRequestBodySize = StaticDetails.FileSizeLimit500MB);
builder.Services.Configure<KestrelServerOptions>(options => options.Limits.MaxRequestBodySize = StaticDetails.FileSizeLimit500MB);
builder.Services.AddControllersWithViews();
builder.Services.AddIdentity<IdentityUser, IdentityRole>().AddDefaultTokenProviders()
    .AddEntityFrameworkStores<SlrmDbContext>();
builder.Services.AddDbContext<SlrmDbContext>(options =>
    options.UseSqlServer(connectionString)
);
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
builder.Services.AddTransient<IEmailSender, EmailSender>();
builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
builder.Services.AddRazorPages();
builder.Services.ConfigureApplicationCookie(options => {
    options.LoginPath = $"/Identity/Account/Login";
    options.LogoutPath = $"/Identity/Account/Logout";
    options.AccessDeniedPath = $"/Identity/Account/AccessDenied";
});
builder.Services.AddDistributedMemoryCache();

builder.Services.AddSession(options => {
    options.IdleTimeout = TimeSpan.FromMinutes(100);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

builder.Services.AddAuthorizationBuilder()
    .AddPolicy("AdministratorRights",
        policy => policy.RequireRole(StaticDetails.RoleSystemAdmin, StaticDetails.RolePastor))
    .AddPolicy("ElevatedRights",
        policy => policy.RequireRole(StaticDetails.RoleSystemAdmin, StaticDetails.RolePastor,
            StaticDetails.RoleRoadChaplain))
    .AddPolicy("PrayerTeam",
        policy => policy.RequireRole(StaticDetails.RolePrayerTeam, StaticDetails.RolePastor,
            StaticDetails.RoleSystemAdmin))
    .AddPolicy("ModerationRights",
        policy => policy.RequireRole(StaticDetails.RoleSystemAdmin, StaticDetails.RolePastor,
            StaticDetails.RoleRoadChaplain, StaticDetails.RoleTrustedContributor))
    .AddPolicy("UserRights",
        policy => policy.RequireRole(StaticDetails.RoleSystemAdmin, StaticDetails.RolePastor,
            StaticDetails.RoleRoadChaplain, StaticDetails.RoleTrustedContributor,
            StaticDetails.RoleEndUser));

var environment = builder.Services.BuildServiceProvider().GetRequiredService<IWebHostEnvironment>();

builder.Services.AddDataProtection()
    .SetApplicationName($"SpiritLeadRevivalMinistries-{environment.EnvironmentName}").PersistKeysToFileSystem(
        new DirectoryInfo($@"{environment.ContentRootPath}/keys"));

builder.Services.AddSeoHelper(siteName: "SpiritLead Revival Ministries",
                                siteUrl: "https://spiritleadrevival.org/");



var app = builder.Build();

#if  DEBUG
ServicePointManager.ServerCertificateValidationCallback +=
    (sender, certificate, chain, sslPolicyErrors) => true;

#endif

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment()) {
    app.UseDeveloperExceptionPage();
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}
if (app.Environment.IsDevelopment()) {
    app.UseDeveloperExceptionPage();
    app.UseExceptionHandler("/Home/Error");
} else {
    app.UseExceptionHandler("/Home/Error");
}
app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseSession();
app.UseXMLSitemap(environment.ContentRootPath);
app.UseRobotsTxt(environment.ContentRootPath);
app.MapRazorPages();

app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{area=User}/{controller=Home}/{action=Index}/{id?}");

app.Run();
