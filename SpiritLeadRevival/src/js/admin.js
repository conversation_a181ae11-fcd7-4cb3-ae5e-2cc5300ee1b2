// Import main app functionality
import { SpiritLeadApp } from './main.js';

// Admin-specific functionality
class AdminApp extends Spirit<PERSON>eadApp {
  constructor() {
    super();
    this.initAdmin();
  }

  initAdmin() {
    this.setupSidebar();
    this.setupDataTables();
    this.setupDeleteConfirmations();
    this.setupBulkActions();
    this.setupImagePreviews();
    this.setupProgressBars();
  }

  setupSidebar() {
    const sidebar = document.querySelector('.admin-sidebar');
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const overlay = document.querySelector('.mobile-overlay');
    
    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', () => {
        if (window.innerWidth <= 768) {
          // Mobile: show/hide sidebar
          sidebar?.classList.toggle('show');
          overlay?.classList.toggle('show');
        } else {
          // Desktop: collapse/expand sidebar
          sidebar?.classList.toggle('collapsed');
        }
      });
    }

    // Close sidebar when clicking overlay
    overlay?.addEventListener('click', () => {
      sidebar?.classList.remove('show');
      overlay?.classList.remove('show');
    });

    // Handle submenu toggles
    document.querySelectorAll('.nav-item-submenu > .nav-link').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const submenu = link.nextElementSibling;
        const isOpen = submenu?.classList.contains('show');
        
        // Close all other submenus
        document.querySelectorAll('.nav-submenu.show').forEach(menu => {
          menu.classList.remove('show');
        });
        
        // Toggle current submenu
        if (!isOpen) {
          submenu?.classList.add('show');
        }
      });
    });

    // Auto-collapse sidebar on mobile when window resizes
    window.addEventListener('resize', () => {
      if (window.innerWidth > 768) {
        sidebar?.classList.remove('show');
        overlay?.classList.remove('show');
      }
    });
  }

  setupDataTables() {
    // Enhanced table functionality
    document.querySelectorAll('.admin-table').forEach(table => {
      this.makeTableResponsive(table);
      this.addTableSearch(table);
      this.addTableSort(table);
    });
  }

  makeTableResponsive(table) {
    // Add data labels for mobile view
    const headers = table.querySelectorAll('thead th');
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
      const cells = row.querySelectorAll('td');
      cells.forEach((cell, index) => {
        if (headers[index]) {
          cell.setAttribute('data-label', headers[index].textContent.trim());
        }
      });
    });
  }

  addTableSearch(table) {
    const container = table.closest('.admin-table-container');
    if (!container) return;

    // Create search input
    const searchContainer = document.createElement('div');
    searchContainer.className = 'table-search mb-3';
    searchContainer.innerHTML = `
      <div class="input-group">
        <span class="input-group-text">
          <i class="bi bi-search"></i>
        </span>
        <input type="text" class="form-control" placeholder="Search table...">
      </div>
    `;

    container.insertBefore(searchContainer, table);

    const searchInput = searchContainer.querySelector('input');
    searchInput.addEventListener('input', (e) => {
      this.filterTable(table, e.target.value);
    });
  }

  filterTable(table, searchTerm) {
    const rows = table.querySelectorAll('tbody tr');
    const term = searchTerm.toLowerCase();

    rows.forEach(row => {
      const text = row.textContent.toLowerCase();
      row.style.display = text.includes(term) ? '' : 'none';
    });
  }

  addTableSort(table) {
    const headers = table.querySelectorAll('thead th');
    
    headers.forEach((header, index) => {
      if (header.classList.contains('no-sort')) return;
      
      header.style.cursor = 'pointer';
      header.innerHTML += ' <i class="bi bi-arrow-down-up ms-1"></i>';
      
      header.addEventListener('click', () => {
        this.sortTable(table, index);
      });
    });
  }

  sortTable(table, columnIndex) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const isAscending = !table.dataset.sortAsc || table.dataset.sortAsc === 'false';
    
    rows.sort((a, b) => {
      const aText = a.cells[columnIndex]?.textContent.trim() || '';
      const bText = b.cells[columnIndex]?.textContent.trim() || '';
      
      // Try to parse as numbers
      const aNum = parseFloat(aText);
      const bNum = parseFloat(bText);
      
      if (!isNaN(aNum) && !isNaN(bNum)) {
        return isAscending ? aNum - bNum : bNum - aNum;
      }
      
      // Sort as strings
      return isAscending ? 
        aText.localeCompare(bText) : 
        bText.localeCompare(aText);
    });
    
    // Update table
    rows.forEach(row => tbody.appendChild(row));
    table.dataset.sortAsc = isAscending.toString();
    
    // Update sort indicators
    table.querySelectorAll('thead th i').forEach(icon => {
      icon.className = 'bi bi-arrow-down-up ms-1';
    });
    
    const currentHeader = table.querySelectorAll('thead th')[columnIndex];
    const icon = currentHeader.querySelector('i');
    if (icon) {
      icon.className = isAscending ? 
        'bi bi-arrow-up ms-1' : 
        'bi bi-arrow-down ms-1';
    }
  }

  setupDeleteConfirmations() {
    document.querySelectorAll('a[href*="Delete"], button[data-action="delete"]').forEach(element => {
      element.addEventListener('click', async (e) => {
        e.preventDefault();
        
        const result = await this.confirmDelete(element);
        if (result.isConfirmed) {
          if (element.tagName === 'A') {
            window.location.href = element.href;
          } else {
            // Handle form submission or AJAX delete
            const form = element.closest('form');
            if (form) {
              form.submit();
            }
          }
        }
      });
    });
  }

  setupBulkActions() {
    const selectAllCheckbox = document.querySelector('#selectAll');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    const bulkActionBtn = document.querySelector('#bulkActionBtn');
    
    if (selectAllCheckbox && itemCheckboxes.length > 0) {
      selectAllCheckbox.addEventListener('change', (e) => {
        itemCheckboxes.forEach(checkbox => {
          checkbox.checked = e.target.checked;
        });
        this.updateBulkActionButton();
      });
      
      itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
          this.updateBulkActionButton();
        });
      });
    }
  }

  updateBulkActionButton() {
    const checkedItems = document.querySelectorAll('.item-checkbox:checked');
    const bulkActionBtn = document.querySelector('#bulkActionBtn');
    
    if (bulkActionBtn) {
      bulkActionBtn.disabled = checkedItems.length === 0;
      bulkActionBtn.textContent = `Bulk Actions (${checkedItems.length})`;
    }
  }

  setupImagePreviews() {
    document.querySelectorAll('input[type="file"][accept*="image"]').forEach(input => {
      input.addEventListener('change', (e) => {
        this.previewImage(e.target);
      });
    });
  }

  previewImage(input) {
    const file = input.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e) => {
      let preview = input.parentElement.querySelector('.image-preview');
      if (!preview) {
        preview = document.createElement('div');
        preview.className = 'image-preview mt-2';
        input.parentElement.appendChild(preview);
      }
      
      preview.innerHTML = `
        <img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
        <button type="button" class="btn btn-sm btn-danger ms-2" onclick="this.parentElement.remove()">
          <i class="bi bi-trash"></i>
        </button>
      `;
    };
    reader.readAsDataURL(file);
  }

  setupProgressBars() {
    // Enhanced progress bar functionality for file uploads
    window.startUpdatingProgressIndicator = () => {
      const progressContainer = document.getElementById('progress');
      const uploadStatus = document.getElementById('upload-status');
      
      if (progressContainer) progressContainer.style.display = 'block';
      if (uploadStatus) uploadStatus.style.display = 'none';
      
      const intervalId = setInterval(() => {
        fetch('/Admin/VideoFile/Progress', { method: 'POST' })
          .then(response => response.text())
          .then(progress => {
            const progressBar = document.querySelector('.progress-bar');
            if (progressBar) {
              progressBar.style.width = progress + '%';
              progressBar.setAttribute('aria-valuenow', progress);
              progressBar.textContent = progress + '%';
            }
            
            if (parseInt(progress) >= 100) {
              clearInterval(intervalId);
              if (progressContainer) progressContainer.style.display = 'none';
              if (uploadStatus) uploadStatus.style.display = 'block';
            }
          })
          .catch(error => {
            console.error('Progress update failed:', error);
            clearInterval(intervalId);
          });
      }, 1000);
    };
  }

  // Enhanced file upload with modern fetch API
  async uploadFiles(inputId) {
    const input = document.getElementById(inputId);
    const files = input.files;
    
    if (files.length === 0) {
      this.showNotification('Please select files to upload', 'warning');
      return;
    }
    
    const formData = new FormData();
    Array.from(files).forEach(file => {
      formData.append('files', file);
    });
    
    try {
      this.startUpdatingProgressIndicator();
      
      const response = await fetch('/Upload', {
        method: 'POST',
        body: formData
      });
      
      if (response.ok) {
        this.showNotification('Files uploaded successfully!', 'success');
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      this.showNotification('Upload failed. Please try again.', 'error');
    }
  }
}

// Initialize admin app
document.addEventListener('DOMContentLoaded', () => {
  window.adminApp = new AdminApp();
});

// Legacy function support
window.uploadFiles = (inputId) => {
  if (window.adminApp) {
    window.adminApp.uploadFiles(inputId);
  }
};

export { AdminApp };
