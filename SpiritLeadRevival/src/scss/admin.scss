// Import main styles
@import "main";

// Admin Layout
.admin-layout {
  display: flex;
  min-height: 100vh;
  
  .admin-sidebar {
    width: $admin-sidebar-width;
    background: linear-gradient(180deg, var(--slr-dark) 0%, #1a1a1a 100%);
    color: var(--slr-text-white);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    overflow-y: auto;
    transition: var(--slr-transition-slow);
    z-index: 1000;
    
    &.collapsed {
      width: $admin-sidebar-width-collapsed;
      
      .sidebar-brand-text,
      .nav-link-text {
        opacity: 0;
        visibility: hidden;
      }
      
      .nav-link {
        justify-content: center;
      }
    }
    
    @media (max-width: $admin-mobile-breakpoint) {
      transform: translateX(-100%);
      
      &.show {
        transform: translateX(0);
      }
    }
  }
  
  .admin-main {
    flex: 1;
    margin-left: $admin-sidebar-width;
    transition: var(--slr-transition-slow);
    
    .admin-sidebar.collapsed + & {
      margin-left: $admin-sidebar-width-collapsed;
    }
    
    @media (max-width: $admin-mobile-breakpoint) {
      margin-left: 0;
    }
  }
}

// Admin Header
.admin-header {
  background: var(--slr-bg-secondary);
  height: $admin-header-height;
  display: flex;
  align-items: center;
  padding: 0 var(--slr-spacer-lg);
  box-shadow: var(--slr-shadow-sm);
  position: sticky;
  top: 0;
  z-index: 999;
  
  .sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--slr-text-primary);
    cursor: pointer;
    padding: var(--slr-spacer-sm);
    border-radius: var(--slr-border-radius);
    transition: var(--slr-transition);
    
    &:hover {
      background-color: var(--slr-bg-tertiary);
    }
    
    @media (min-width: $admin-mobile-breakpoint + 1px) {
      display: none;
    }
  }
  
  .breadcrumb {
    background: none;
    padding: 0;
    margin: 0 0 0 var(--slr-spacer);
    
    .breadcrumb-item {
      &.active {
        color: var(--slr-primary);
        font-weight: 600;
      }
    }
  }
}

// Sidebar Navigation
.sidebar-brand {
  padding: var(--slr-spacer-lg);
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  .sidebar-brand-icon {
    font-size: 2rem;
    margin-bottom: var(--slr-spacer-sm);
  }
  
  .sidebar-brand-text {
    font-size: 1.25rem;
    font-weight: 700;
    transition: var(--slr-transition);
  }
}

.sidebar-nav {
  padding: var(--slr-spacer) 0;
  
  .nav-item {
    margin-bottom: var(--slr-spacer-sm);
    
    .nav-link {
      display: flex;
      align-items: center;
      padding: var(--slr-spacer-sm) var(--slr-spacer-lg);
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      transition: var(--slr-transition);
      border-radius: 0;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--slr-text-white);
      }
      
      &.active {
        background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
        color: var(--slr-text-white);
      }
      
      .nav-link-icon {
        width: 1.5rem;
        text-align: center;
        margin-right: var(--slr-spacer);
        transition: var(--slr-transition);
      }
      
      .nav-link-text {
        transition: var(--slr-transition);
      }
    }
    
    // Submenu styles
    .nav-submenu {
      max-height: 0;
      overflow: hidden;
      transition: var(--slr-transition-slow);
      background-color: rgba(0, 0, 0, 0.2);
      
      &.show {
        max-height: 500px;
      }
      
      .nav-link {
        padding-left: calc(var(--slr-spacer-lg) + 2rem);
        font-size: 0.9rem;
      }
    }
  }
}

// Admin Content
.admin-content {
  padding: var(--slr-spacer-lg);
  
  @media (max-width: 768px) {
    padding: var(--slr-spacer);
  }
}

// Admin Cards
.admin-card {
  background: var(--slr-bg-secondary);
  border-radius: var(--slr-border-radius-lg);
  box-shadow: var(--slr-shadow);
  margin-bottom: var(--slr-spacer-lg);
  
  .admin-card-header {
    background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
    color: var(--slr-text-white);
    padding: var(--slr-spacer-lg);
    border-radius: var(--slr-border-radius-lg) var(--slr-border-radius-lg) 0 0;
    
    .admin-card-title {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
    }
    
    .admin-card-actions {
      display: flex;
      gap: var(--slr-spacer-sm);
      
      .btn {
        padding: var(--slr-spacer-sm) var(--slr-spacer);
        font-size: 0.875rem;
      }
    }
  }
  
  .admin-card-body {
    padding: var(--slr-spacer-lg);
  }
}

// Responsive Admin Table
.admin-table-container {
  background: var(--slr-bg-secondary);
  border-radius: var(--slr-border-radius-lg);
  box-shadow: var(--slr-shadow);
  overflow: hidden;
  
  .admin-table {
    margin: 0;
    
    thead th {
      background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
      color: var(--slr-text-white);
      border: none;
      padding: var(--slr-spacer) var(--slr-spacer-lg);
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.875rem;
      letter-spacing: 0.5px;
    }
    
    tbody {
      tr {
        transition: var(--slr-transition);
        
        &:hover {
          background-color: rgba(63, 154, 214, 0.05);
        }
        
        td {
          padding: var(--slr-spacer) var(--slr-spacer-lg);
          border-top: 1px solid var(--slr-border-light);
          vertical-align: middle;
        }
      }
    }
  }
  
  // Mobile responsive table
  @media (max-width: 768px) {
    .admin-table {
      thead {
        display: none;
      }
      
      tbody {
        tr {
          display: block;
          margin-bottom: var(--slr-spacer);
          background: var(--slr-bg-secondary);
          border-radius: var(--slr-border-radius);
          box-shadow: var(--slr-shadow-sm);
          
          td {
            display: block;
            padding: var(--slr-spacer-sm) var(--slr-spacer);
            border: none;
            border-bottom: 1px solid var(--slr-border-light);
            
            &:last-child {
              border-bottom: none;
            }
            
            &:before {
              content: attr(data-label) ": ";
              font-weight: 600;
              color: var(--slr-primary);
            }
          }
        }
      }
    }
  }
}

// Action buttons
.admin-actions {
  display: flex;
  gap: var(--slr-spacer-sm);
  flex-wrap: wrap;
  
  .btn {
    padding: var(--slr-spacer-sm) var(--slr-spacer);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: var(--slr-spacer-sm);
    
    i {
      font-size: 1rem;
    }
  }
  
  @media (max-width: 480px) {
    flex-direction: column;
    
    .btn {
      justify-content: center;
    }
  }
}

// Mobile overlay
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: var(--slr-transition);
  
  &.show {
    opacity: 1;
    visibility: visible;
  }
  
  @media (min-width: $admin-mobile-breakpoint + 1px) {
    display: none;
  }
}

// Form improvements for admin
.admin-form {
  .form-group {
    margin-bottom: var(--slr-spacer-lg);
    
    label {
      font-weight: 600;
      color: var(--slr-text-primary);
      margin-bottom: var(--slr-spacer-sm);
    }
    
    .form-control {
      padding: var(--slr-spacer) var(--slr-spacer-lg);
    }
    
    .form-text {
      color: var(--slr-text-secondary);
      font-size: 0.875rem;
    }
  }
}
