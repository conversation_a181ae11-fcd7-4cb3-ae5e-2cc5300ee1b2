// Import Bootstrap
@import "bootstrap/scss/bootstrap";

// Import custom variables
@import "variables";

// Base styles
body {
  background-color: var(--slr-bg-primary);
  font-family: var(--slr-font-family-sans-serif);
  line-height: var(--slr-line-height-base);
}

// Header styles
.site-header {
  background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
  border-radius: 0 0 var(--slr-border-radius-lg) var(--slr-border-radius-lg);
  box-shadow: var(--slr-shadow);
  
  .logo {
    max-width: 350px;
    height: auto;
    transition: var(--slr-transition);
    
    @media (max-width: 768px) {
      max-width: 250px;
    }
    
    @media (max-width: 480px) {
      max-width: 200px;
    }
  }
}

// Navigation styles
.nav-link {
  transition: var(--slr-transition);
  border-radius: var(--slr-border-radius);
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
  }
  
  svg {
    transition: var(--slr-transition);
  }
  
  &:hover svg {
    transform: scale(1.1);
  }
}

// Dropdown improvements
.dropdown-menu {
  border: none;
  box-shadow: var(--slr-shadow-lg);
  border-radius: var(--slr-border-radius-lg);
  padding: var(--slr-spacer-sm);
  
  .dropdown-item {
    border-radius: var(--slr-border-radius);
    transition: var(--slr-transition);
    
    &:hover {
      background-color: var(--slr-primary);
      color: var(--slr-text-white);
      transform: translateX(4px);
    }
  }
}

// Card improvements
.card {
  border: none;
  box-shadow: var(--slr-shadow);
  border-radius: var(--slr-border-radius-lg);
  transition: var(--slr-transition);
  
  &:hover {
    box-shadow: var(--slr-shadow-lg);
    transform: translateY(-2px);
  }
  
  .card-header {
    background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
    color: var(--slr-text-white);
    border-radius: var(--slr-border-radius-lg) var(--slr-border-radius-lg) 0 0;
    border: none;
  }
}

// Button improvements
.btn {
  border-radius: var(--slr-border-radius);
  transition: var(--slr-transition);
  font-weight: 500;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--slr-shadow);
  }
  
  &.btn-primary {
    background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
    border: none;
  }
  
  &.btn-success {
    background: linear-gradient(135deg, var(--slr-success) 0%, #218838 100%);
    border: none;
  }
  
  &.btn-danger {
    background: linear-gradient(135deg, var(--slr-danger) 0%, #c82333 100%);
    border: none;
  }
}

// Form improvements
.form-control {
  border-radius: var(--slr-border-radius);
  border: 2px solid var(--slr-border-light);
  transition: var(--slr-transition);
  
  &:focus {
    border-color: var(--slr-primary);
    box-shadow: 0 0 0 0.2rem rgba(63, 154, 214, 0.25);
  }
}

// Table improvements
.table {
  background-color: var(--slr-bg-secondary);
  border-radius: var(--slr-border-radius-lg);
  overflow: hidden;
  box-shadow: var(--slr-shadow);
  
  thead th {
    background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
    color: var(--slr-text-white);
    border: none;
    font-weight: 600;
  }
  
  tbody tr {
    transition: var(--slr-transition);
    
    &:hover {
      background-color: rgba(63, 154, 214, 0.05);
    }
  }
}

// Footer styles
.site-footer {
  background-color: var(--slr-bg-secondary);
  border-top: 1px solid var(--slr-border-light);
  margin-top: var(--slr-spacer-xl);
}

// Utility classes
.shadow-custom {
  box-shadow: var(--slr-shadow) !important;
}

.shadow-custom-lg {
  box-shadow: var(--slr-shadow-lg) !important;
}

.rounded-custom {
  border-radius: var(--slr-border-radius-lg) !important;
}

.text-gradient {
  background: linear-gradient(135deg, var(--slr-primary) 0%, #2980b9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// Responsive improvements
@media (max-width: 768px) {
  .container {
    padding-left: var(--slr-spacer);
    padding-right: var(--slr-spacer);
  }
  
  .site-header {
    .container {
      padding-left: var(--slr-spacer-sm);
      padding-right: var(--slr-spacer-sm);
    }
  }
  
  .table-responsive {
    border-radius: var(--slr-border-radius-lg);
  }
}

// Loading states
.loading {
  position: relative;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
  }
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

// Scroll improvements
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--slr-bg-tertiary);
  border-radius: var(--slr-border-radius);
}

::-webkit-scrollbar-thumb {
  background: var(--slr-primary);
  border-radius: var(--slr-border-radius);
  
  &:hover {
    background: #2980b9;
  }
}
