// Brand Colors
:root {
  --slr-primary: #3F9AD6;
  --slr-secondary: #eee6d3;
  --slr-accent: #1b6ec2;
  --slr-success: #28a745;
  --slr-warning: #ffc107;
  --slr-danger: #dc3545;
  --slr-info: #17a2b8;
  --slr-light: #f8f9fa;
  --slr-dark: #212529;
  
  // Background Colors
  --slr-bg-primary: #eee6d3;
  --slr-bg-secondary: #ffffff;
  --slr-bg-tertiary: #f8f9fa;
  
  // Text Colors
  --slr-text-primary: #212529;
  --slr-text-secondary: #6c757d;
  --slr-text-muted: #868e96;
  --slr-text-white: #ffffff;
  
  // Border Colors
  --slr-border-color: #dee2e6;
  --slr-border-light: #e9ecef;
  
  // Shadow
  --slr-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --slr-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --slr-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  
  // Border Radius
  --slr-border-radius: 0.375rem;
  --slr-border-radius-sm: 0.25rem;
  --slr-border-radius-lg: 0.5rem;
  --slr-border-radius-xl: 1rem;
  
  // Spacing
  --slr-spacer: 1rem;
  --slr-spacer-sm: 0.5rem;
  --slr-spacer-lg: 1.5rem;
  --slr-spacer-xl: 3rem;
  
  // Typography
  --slr-font-family-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif;
  --slr-font-size-base: 1rem;
  --slr-font-size-sm: 0.875rem;
  --slr-font-size-lg: 1.25rem;
  --slr-line-height-base: 1.5;
  
  // Transitions
  --slr-transition: all 0.15s ease-in-out;
  --slr-transition-fast: all 0.1s ease-in-out;
  --slr-transition-slow: all 0.3s ease-in-out;
}

// Bootstrap Variable Overrides
$primary: var(--slr-primary);
$secondary: var(--slr-secondary);
$success: var(--slr-success);
$info: var(--slr-info);
$warning: var(--slr-warning);
$danger: var(--slr-danger);
$light: var(--slr-light);
$dark: var(--slr-dark);

$body-bg: var(--slr-bg-primary);
$body-color: var(--slr-text-primary);

$font-family-sans-serif: var(--slr-font-family-sans-serif);
$font-size-base: var(--slr-font-size-base);
$line-height-base: var(--slr-line-height-base);

$border-radius: var(--slr-border-radius);
$border-radius-sm: var(--slr-border-radius-sm);
$border-radius-lg: var(--slr-border-radius-lg);

$box-shadow-sm: var(--slr-shadow-sm);
$box-shadow: var(--slr-shadow);
$box-shadow-lg: var(--slr-shadow-lg);

$spacer: var(--slr-spacer);

// Breakpoints
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

// Container max widths
$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px
);

// Admin specific variables
$admin-sidebar-width: 280px;
$admin-sidebar-width-collapsed: 80px;
$admin-header-height: 70px;
$admin-mobile-breakpoint: 768px;
