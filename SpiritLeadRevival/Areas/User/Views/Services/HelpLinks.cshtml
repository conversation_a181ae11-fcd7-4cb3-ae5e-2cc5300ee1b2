@model IEnumerable<MinistryHelpLink>
@{
ViewData["Title"] = "Help Links";
}
@section SEO {
    <meta name="description" content="Help page for SpiritLead Revival. We all need a little hands up at times. These are som various places that might be able to help."/>
}
<div class="container-fluid bg-light">
    <h1 class="text-center text-primary">Help Links</h1>
    <div class="col-12 bg-light border rounded-3 p-3">
        <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3 pb-3">
            @foreach (var link in Model) {
                <div class="col">
                    <div class="card  shadow-sm" style="">
                        <img src="@link.ImageUrl" alt="@link.Name" style="max-height: 150px; width: auto; object-fit: scale-down" />
                        <div class="card-body">
                            <h5 class="card-title">@link.Name</h5>
                            <p class="card-text">@Html.Raw(link.Description)</p>
                            <a href="@link.Url" class="btn btn-primary">Visit @link.Name</a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>