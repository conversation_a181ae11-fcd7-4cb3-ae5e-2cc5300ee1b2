@using Microsoft.AspNetCore.Mvc.TagHelpers
@model PrayerRequestVm
@{
    ViewData["Title"] = "Prayer Request";
}
@section SEO {
    <meta name="description" content="Prayer Request page for SpiritLead Revival."/>
}
@section Scripts {
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}
<div class="container text-center">
    <div class="row">
        <div class="col-lg-6 col-md-12">
            <div class="border bg-light pt-3 mt-4 rounded-3">
                <div class="row pb-1">
                    <h1 class="text-primary">Praise Reports</h1>
                    <hr />
                </div>
                <div class="row scroll" style="max-height: 40rem; overflow:hidden;">
                    <div class="list-group scroll-praise-reports">
                        @if (Model.PraiseReports is not null && Model.PraiseReports.Any())
                        {
                            @foreach (var item in Model.PraiseReports)
                            {
                                <div href="#" class="list-group-item list-group-item-action d-flex gap-3 py-3" aria-current="true">
                                    <div class="d-flex gap-2 w-100 justify-content-between">
                                        <div class="container">
                                            <div class="d-flex justify-content-between justify-content-start">
                                                <h6 class="mb-2 w-100 text-center">@Html.Raw(item.Title)</h6>
                                                <h6>@Html.Raw(item.DatePosted)</h6>
                                            </div>
                                            <div class="scroll">
                                                <p class="mb-0 opacity-75 bg-light">@Html.Raw(item.Content)</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <span class="h6">
                                No Content right now.
                            </span>
                        }
                    </div>
                </div>

            </div>
        </div>
        <div class="col-lg-6 col-md-12">
            <form method="post">
                <div class="border bg-light p-3 my-4 rounded-3">
                    <div class="row pb-2">
                        <h1 class="text-primary">Send Prayer Request</h1>
                        <hr/>
                    </div>
                    <div class="mb-3">
                        <label asp-for="Request.FromName"></label>
                        <input asp-for="Request.FromName" class="form-control"/>
                        <span asp-validation-for="Request.FromName" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label asp-for="Request.FromEmail"></label>
                        <input asp-for="Request.FromEmail" class="form-control"/>
                        <span asp-validation-for="Request.FromEmail" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label asp-for="Request.Subject"></label>
                        <input asp-for="Request.Subject" class="form-control"/>
                        <span asp-validation-for="Request.Subject" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label asp-for="Request.Body"></label>
                        <textarea id="mce" asp-for="Request.Body" rows="6" class="form-control"></textarea>
                        <span asp-validation-for="Request.Body" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <div class="g-recaptcha" data-sitekey="6LeXGeAfAAAAAN9TPAVWCNdDcGNaoMryBMWwYI80"></div>
                    </div>
                    <button type="submit" class="btn btn-primary" style="width:150px">Send</button>
                </div>
            </form>
        </div>

    </div>
    
</div>
