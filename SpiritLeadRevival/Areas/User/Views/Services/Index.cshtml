@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ServiceVm
@{
    ViewData["Title"] = "Services";
}
@section SEO {
    <meta name="description" content="Services page for SpiritLead Revival."/>
}
<div class="row bg-light border rounded-3 px-3 mt-3 d-flex justify-content-center">
    <div class="row text-center pt-3 pb-3">
            <h1 class="text-primary">Our Ministries</h1>
    </div>
    <div class="col-4 text-center border rounded-3 m-3 p-3">
        <div class="card">

            <div class="card-body">
                <h5 class="card-title text-primary">Prayer Requests</h5>
                <p class="card-text">Supporting one another through prayer</p>
                <a asp-area="User" asp-controller="Services" asp-action="PrayerRequest" class="btn btn-primary">Prayer Request</a>
            </div>
        </div>
    </div>
    <div class="col-4 text-center border rounded-3 m-3 p-3">
        <div class="card">

            <div class="card-body">
                <h5 class="card-title text-primary">Links for Help</h5>
                <p class="card-text">Links and help for those who need it.</p>
                <a asp-area="User" asp-controller="Services" asp-action="HelpLinks" class="btn btn-primary">Links for help</a>
            </div>
        </div>
    </div>
    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3 pb-3">
        @foreach (var description in Model.MinistryDescriptions)
        {
            <div class="col">
                <div class="card shadow-sm">
                    <div class="card-header text-center">
                        <h3 class="text-primary">@description.Name</h3>
                    </div>
                    <div class="card-body">
                        @*<h5 class="card-title">@description.Name</h5>*@
                        <p class="card-text">@Html.Raw(description.Description)</p>
                        <div class="d-flex justify-content-between align-items-center">
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

