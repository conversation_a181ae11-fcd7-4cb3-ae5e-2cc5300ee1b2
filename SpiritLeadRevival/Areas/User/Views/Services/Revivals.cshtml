@model RevivalVm
@{
    ViewData["Title"] = "Revivals";
}
@section SEO {
<meta name="description"
      content="Revivals page for SpiritLead Revival. Here you will find a list of all the diffrent revival conferences that we have coming up."
      xmlns="http://www.w3.org/1999/html"/>
}
<div class="row">
    <div class="container bg-light rounded-3 my-3">
        <div class="row text-center">
            <h1 class="text-primary" style="font-family: 'Poiret One'">Revival Services</h1>
        </div>
        <div class="row">
            <div class="col-2"></div>
            <div class="col-8 poiret">
                <p>Revival Services have been laid deeply in our hearts by the Lord. Because each church is unique in its own people, we believe that all revival services are to be Spirit lead.</p>
                
                <p>The purpose for Revival Services is to awaken the Church by focusing on the members of the body. 
                    With the Leading of the Holy Spirit, they will be awakened to their individual calling God has placed on their lives. 
                    Then they will be able to recognize the position they have been called to. There will be a stronger 
                    uniting force with one other in the body of the church, 
                    resulting in more people reaching out to one other and communities, 
                    helping those who are in need of a spiritual awakening.</p>
                
                <p>We believe in the power of God to save, heal, deliver and baptize the spirit in the Holy Ghost. 
                    We believe when the body is united together, and actively serving the Lord in His Fullness and Power, 
                    the Church will flourish beyond any plans they could have made. 
                    All of this will be caused by becoming a living body in Christ Jesus, 
                    with leaders who are listening closely to the direction of the Lord.</p>
                <p>Check the list of revivals! Come let us see what God wants to do in your Church!</p>
            </div>
            <div class="col-2"></div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-12 col-md-6 g-3">
        <div class="col bg-light rounded-3 border mb-3 p-3">
            <div class="text-center bold border-bottom">
                    <strong class="text-primary">Revivals</strong>
            </div>
            <div class="list-group">
                @if (!Model.Conferences.Any())
                {
                    <span class="h3 text-center">No Revivals Scheduled</span>
                }
                @foreach (var entry in Model.Conferences)
                {
                    <a href="#" class="list-group-item list-group-item-action d-flex gap-3 py-3" aria-current="true">
                        @*<img src="https://github.com/twbs.png" alt="twbs" width="32" height="32" class="rounded-circle flex-shrink-0">*@
                        <div class="d-flex gap-2 w-100 justify-content-between">
                            <div class="container">
                                <h6 class="mb-0 w-100 text-center">@entry.City ,@entry.State</h6>
                                <div class="scroll">
                                    <p class="mb-0 opacity-75">@Html.Raw(entry.Description)</p>
                                </div>
                            </div>
                            <small class="opacity-50 text-nowrap">@entry.EventDateTime.ToShortDateString()</small>
                        </div>
                    </a>
                }
            </div>
        </div> 
        @* <div class="col bg-light rounded-3 border mb-3 p-3"> *@
        @*     <div class="text-center bold border-bottom"> *@
        @*         <strong class="text-primary">Principals of Faith</strong> *@
        @*     </div> *@
        @*     <div class="list-group"> *@
        @*         @if (!Model.PrincipalOfFaiths.Any()) *@
        @*         { *@
        @*             <span class="h3">No Conferences Scheduled</span> *@
        @*         } *@
        @*         @foreach (var entry in Model.PrincipalOfFaiths) *@
        @*         { *@
        @*             <a href="#" class="list-group-item list-group-item-action d-flex gap-3 py-3" aria-current="true"> *@
        @*                 <div class="d-flex gap-2 w-100 justify-content-between"> *@
        @*                     <div class="container"> *@
        @*                         <h6 class="mb-0 w-100 text-center">@entry.City ,@entry.State</h6> *@
        @*                         <div class="scroll"> *@
        @*                             <p class="mb-0 opacity-75">@Html.Raw(entry.Description)</p> *@
        @*                         </div> *@
        @*                     </div> *@
        @*                     <small class="opacity-50 text-nowrap">@entry.EventDateTime.ToShortDateString()</small> *@
        @*                 </div> *@
        @*             </a> *@
        @*         } *@
        @*     </div> *@
        @* </div>   *@
        @* <div class="col bg-light rounded-3 border mb-3 p-3">  *@
        @*     <div class="text-center bold border-bottom"> *@
        @*         <strong class="text-primary">Spirit Lead Descisions</strong> *@
        @*     </div> *@
        @*     <div class="list-group"> *@
        @*         @if (!Model.SpiritLeadDecisions.Any()) *@
        @*         { *@
        @*             <span class="h3">No Conferences Scheduled</span> *@
        @*         } *@
        @*         @foreach (var entry in Model.SpiritLeadDecisions) *@
        @*         { *@
        @*             <a href="#" class="list-group-item list-group-item-action d-flex gap-3 py-3" aria-current="true"> *@
        @*                 $1$<img src="https://github.com/twbs.png" alt="twbs" width="32" height="32" class="rounded-circle flex-shrink-0">#1# *@
        @*                 <div class="d-flex gap-2 w-100 justify-content-between"> *@
        @*                     <div class="container"> *@
        @*                         <h6 class="mb-0 w-100 text-center">@entry.City ,@entry.State</h6> *@
        @*                         <div class="scroll"> *@
        @*                             <p class="mb-0 opacity-75">@Html.Raw(entry.Description)</p> *@
        @*                         </div> *@
        @*                     </div> *@
        @*                     <small class="opacity-50 text-nowrap">@entry.EventDateTime.ToShortDateString()</small> *@
        @*                 </div> *@
        @*             </a> *@
        @*         } *@
        @*     </div> *@
        @* </div>  *@
        </div>
    <div class="col-12 col-md-6 g-3">
        <div class="col bg-light rounded-3 border mb-3 p-3">
            <div class="text-center bold border-bottom">
                <strong class="text-primary">References</strong>
            </div>
            <div class="list-group">
                @foreach (var entry in Model.References)
                {
                    <a href="#" class="list-group-item list-group-item-action d-flex gap-3 py-3" aria-current="true">
                        @*<img src="https://github.com/twbs.png" alt="twbs" width="32" height="32" class="rounded-circle flex-shrink-0">*@
                        <div class="d-flex gap-2 w-100 justify-content-between">
                            <div class="container">
                                <h6 class="mb-0 w-100 text-center">@entry.Writer</h6>
                                <div class="scroll">
                                    <p class="mb-0 opacity-75">@Html.Raw(entry.Letter)</p>
                                </div>
                            </div>
                            @*<small class="opacity-50 text-nowrap">@entry.DateWritten.ToShortDateString()</small>*@
                        </div>
                    </a>
                }
            </div>
        </div>
    </div>
</div>
@section Styles {
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poiret+One&display=swap" rel="stylesheet">
<style type="text/css">
    .poiret {
        font-family: 'Poiret One';
        font-size: 20px;
        font-weight: bold;
    }
</style>
}