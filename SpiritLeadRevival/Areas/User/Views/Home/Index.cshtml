@using System.Text
@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.ViewModels.HomeVm
@{
    ViewData["Title"] = "Home";
}

@section SEO {
    <meta name="description" content="We here at SpiritLead Revival Ministries are spiritually invested in the wellness and knowledge of peoples walk with the Lord." />
}

@section Scripts {
    <script type="text/javascript">
        $(function () {
            $("#carouselItem").first().addClass("active");
        });

    </script>
}


<div class="container rounded-3 py-3">
    <div class="row bg-light border rounded-3 p-3">
        <div class="col col-12 col-md-6 g-3">
            <div class="card">
                <div class="card-header text-center">
                    <h3 class="text-primary">Thought and Scripture for day</h3>
                </div>
                <div class="card-body">
                    <blockquote class="blockquote mb-0">
                        @Html.Raw(Model.DailyScripture.Verse)
                    </blockquote>
                </div>
            </div>
        </div>
        <div class="col col-12 col-md-6 g-3">
            <div class="card">
                <div class="card-header text-center">
                    <h3 class="text-primary">Join our socials!</h3>
                </div>
                <div class="card-body">
                    <ul class="list-group">
                        <li class="list-group-item">
                            <span class="fs-2">
                                <a href="https://www.youtube.com/@@SpiritLeadRevival" style="text-decoration:none">
                                    <i class="fa-brands fa-square-youtube"></i> Learn more on YouTube
                                </a>
                            </span>
                        </li>
                        @*<li class="list-group-item">
                            <span class="fs-2"><a href="https://facebook.com"><i class="fa-brands fa-facebook"></i></a> Join us on Facebook</span>
                        </li>*@
                        <li class="list-group-item">
                            <span class="fs-2">
                                <a asp-controller="Home" asp-action="Subscribe" style="text-decoration: none;">
                                    <i class="fa-solid fa-newspaper"></i> Join our newsletter.
                                </a>
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row bg-light border rounded-3 my-3">
        <div class="col col-12 col-md-6 g-3 p-3">

            <div id="carouselExampleCaptions" class="carousel slide" data-bs-ride="carousel" data-bs-interval="10000">
                @{
                    var indicatorHtmlBuilder = new StringBuilder();
                    indicatorHtmlBuilder.Append("<div class=\"carousel-indicators\">\n");
                    for (var i = 0; i < Model.CarouselItems.Count(); i++)
                    {
                        if (i == 0)
                        {
                            indicatorHtmlBuilder.Append("<button type=\"button\" data-bs-target=\"#carouselExampleCaptions\" data-bs-slide-to=\"" + i + "\" class=\"active\" aria-current=\"true\" aria-label=\"Slide " + i + "\"></button>\n");
                        }
                        else
                        {
                            indicatorHtmlBuilder.Append("<button type=\"button\" data-bs-target=\"#carouselExampleCaptions\" data-bs-slide-to=\"" + i + "\" aria-label=\"Slide " + i + "\"></button>\n");
                        }
                    }

                    indicatorHtmlBuilder.Append("</div>\n");

                }
                @Html.Raw(indicatorHtmlBuilder)
                <div id="carouselInner" class="carousel-inner">
                    @foreach (var slide in Model.CarouselItems)
                    {
                        <div id="carouselItem" class="carousel-item">
                            <img src="@slide.ImageUrl" class="w-100 rounded" style="max-height: 400px; min-height: 400px;" alt="...">
                            <div class="carousel-caption">
                                <h4 style="-webkit-text-stroke: 1px #3f9ad6">@slide.Caption</h4>
                            </div>
                        </div>
                    }
                </div>
                <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleCaptions" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleCaptions" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Next</span>
                </button>
            </div>
        </div>

        <div class="col col-12 col-md-6 g-3 p-3">
            <div class="text-center mb-3">
                <span class="h3">
                    <a asp-area="User" asp-controller="Resource" asp-action="ChurchNews">Church News</a>
                </span>
            </div>
            <div class="list-group">
                <div href="#" class="list-group-item list-group-item-action d-flex gap-3 py-3" aria-current="true">
                    <div class="d-flex gap-2 w-100 justify-content-between">
                        <div class="container">
                            <h6 class="mb-2 w-100 text-center">@Model.NewsItems.Title</h6>
                            <div class="scroll">
                                <p class="mb-0 opacity-75 bg-light">@Html.Raw(Model.NewsItems.Content)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row bg-light border rounded-3 px-3">
        <div class="container py-3">
            <div class="card">
                <div class="card-header text-center">
                    <p class="fw-bold h3 text-decoration-underline text-primary">Let us start our day</p>
                </div>
                <div class="card-body">
                    <p class="blockquote">Praising the Lord entering in to his presence with rejoicing and his peace. Earnestly seeking out his direction that he would have us going in that day. So we may honor and glorify God.</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row row-cols-sm-1 row-cols-md-1 row-cols-1 row-cols-lg-12 bg-light border rounded-3 px-3 mt-3">
        @* <div class="col col-lg-4 col-sm-12 col-md-12 col-xs-12">
            <div class="row text-center pt-3 pb-3">
                @* <h3><a asp-area="User" asp-controller="Resource" asp-action="VideoLibrary">Recent Video</a></h3> 
                <div class="row row-cols-1 g-3 p-3 mt-5">
                    <div class="col">
                        <div class="card shadow-sm p-3">
                            <h3>
                                <a href="https://www.youtube.com/@@SpiritLeadRevival" target="_blank" rel="noreferrer noopener" class="p-sm-0 m-sm-0">Visit our YouTube Channel for our latest videos.</a>
                            </h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col col-lg-8 col-md-12 col-sm-12 mb-3"> *@
        <div class="col mb-3">
            <div class="row text-center pt-3 pb-3">
                <h3>
                    <a asp-area="User" asp-controller="Resource" asp-action="Events">Upcoming Events</a>
                </h3>
            </div>
            <div class="row text-center">
                <div class="list-group">
                    @foreach (var entry in Model.Events)
                    {
                        <a href="#" class="list-group-item list-group-item-action d-flex  py-lg-3" aria-current="true">
                            <div class="d-flex">
                                <div class="container">
                                    <h6 class="mb-0 w-100 text-center">@entry.Title - <small class="opacity-50 text-nowrap">@entry.Start.ToShortDateString() - @entry.Start.ToShortTimeString()</small></h6>
                                    <div class="scroll">
                                        <p class="w-100 opacity-75">@Html.Raw(entry.Description)</p>
                                    </div>
                                </div>
                                @* <small class="opacity-50 text-nowrap">@entry.Start.ToShortDateString() - @entry.Start.ToShortTimeString()</small> *@
                            </div>
                        </a>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
