@model SpiritLeadRevival.Models.ViewModels.AboutUsViewModel
@{
    ViewData["Title"] = "About Us";
}

@section SEO {
    <meta name="description" content="Learn more about us here at SpiritLead Revival Minstries."/>
}

<div class="row rounded-3 my-3 pb-3 bg-light">
    <div class="container">
        <div class="row text-center">
            <h1 class="my-3 text-primary">Our @(Model.Pastors.Count() == 1 ? "Pastor" : "Pastors")</h1>
        </div>
        <div class="list-group">
            @foreach (var pastor in Model.Pastors)
            {
                <a class="list-group-item list-group-item-action rounded-3 mt-3" aria-current="true">
                    <div style="float: left;" class="m-3">
                        <span style="text-align: center" class="justify-content-center">
                            <img src="@("../../" + pastor.ImageUrl)" class="img-responsive" style="max-height: 300px; object-fit: cover;" alt="Pastor <PERSON><PERSON>, Founder"/>
                        </span>
                        <h4 class="mb-1">@pastor.Name</h4>
                        <h5>@pastor.Title</h5>
                    </div>
                    <span>
                        @(Html.Raw(pastor.Biography))
                    </span>

                </a>
            }
        </div>
    </div>
</div>
<div class="row rounded-3 pb-3 bg-light">
    <div class="container ">
        <h2 class="text-primary text-center mt-3">Our Board Members</h2>
        <div class="list-group">
            @foreach (var members in Model.BoardMembers)
            {
                <a class="list-group-item list-group-item-action rounded-3 mt-3 " aria-current="true">
                    <div style="float: left;" class="my-2 me-3">
                        <span style="text-align: center" class="justify-content-center">
                            <img src="@("../../" + members.ImageUrl)" class="img-responsive" style="max-height: 300px; object-fit: cover;" alt="@members.Name Board Member"/>
                        </span>
                        <h4 class="mb-1">@members.Name</h4>
                        <h5>@members.Title</h5>
                    </div>
                    <span>
                        @Html.Raw(members.Biography)
                    </span>
                </a>
            }
        </div>
    </div>
</div>
<div class="row rounded-3 pb-3 bg-light mt-3">
    <div class="container">
        <h2 class="text-primary text-center mt-3">Our Staff</h2>
        <div class="list-group">
            @if (!Model.StaffMembers.Any())
            {
                <span>No Staff available</span>
            }
            @foreach (var staff in Model.StaffMembers)
            {
                <a class="list-group-item list-group-item-action rounded-3 mt-3" aria-current="true">
                    <div style="float: left;" class="my-2 me-3">
                        <span class="justify-content-center">
                            <img src="@("../../" + staff.ImageUrl)" class="img img-responsive" style="max-height: 300px; max-width:230px; object-position:center; overflow:hidden;" alt="@staff.Name Staff Member"/>
                        </span>
                        <h4 class="mb-1">@staff.Name</h4>
                        <h5>@staff.Title</h5>
                    </div>
                    <div class="col col-sm-12 col-lg-9">
                        <span>@Html.Raw(staff.Biography)</span>
                    </div>
                    
                </a>
            }
        </div>
    </div>
</div>