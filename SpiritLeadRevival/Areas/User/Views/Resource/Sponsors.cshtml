@model IEnumerable<Sponsor>
@{
    ViewData["Title"] = "Sponsors";
}
@section SEO {
    <meta name="description" content="Sponsors page for SpiritLead Revival. These Sponsor make it work."/>
}
<div class="container-fluid bg-light">
    <span class="h3 fw-bold mb-3"> We thank these organizations for their generous sponsorship of Spirit Lead Revival Ministries.</span>
    <div class="col-12 bg-light border rounded-3 p-3">
        <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3 pb-3">
            @foreach (var link in Model) {
                <div class="col">
                    <div class="card  shadow-sm" style="">
                        <img src="@("../../"+link.ImageUrl)" style="max-height: 150px; width: auto; object-fit: scale-down"/>
                        <div class="card-body">
                            <h5 class="card-title">@link.Name</h5>
                            <p class="card-text">@Html.Raw(link.Description)</p>
                            <a href="@link.Url" class="btn btn-primary">Visit @link.Name</a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>