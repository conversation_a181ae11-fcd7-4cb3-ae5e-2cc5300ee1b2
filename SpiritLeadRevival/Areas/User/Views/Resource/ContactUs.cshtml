@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContactVm
@{
ViewData["Title"] = "Contact Us";
}
@section SEO {
    <meta name="description" content="Contact us page for SpiritLead Revival."/>
}
@section Scripts {
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
}
<div class="bg-light border rounded-3 p-3 col-lg-6 col-md-12 mb-3 pb-3 pt-3 mt-3 offset-lg-3">
        <h1 class="text-primary">Contact Us</h1>
    <form method="post">

        <table style="width: 100%; max-width: 550px; border: 0;" cellpadding="8" cellspacing="0">
            <tr>
                <td>
                    <label asp-for="Name">Name*:</label>
                </td>
                <td>
                    <input asp-for="Name" type="text" maxlength="60" style="width: 100%; max-width: 250px;"/>
                </td>
            </tr>
            <tr>
                <td>
                    <label asp-for="PhoneNumber">Phone number:</label>
                </td>
                <td>
                    <input asp-for="PhoneNumber" type="text" maxlength="43" style="width: 100%; max-width: 250px;"/>
                </td>
            </tr>
            <tr>
                <td>
                    <label asp-for="FromEmail">Email address*:</label>
                </td>
                <td>
                    <input asp-for="FromEmail" type="text" maxlength="90" style="width: 100%; max-width: 250px;"/>
                </td>
            </tr>
            <tr>
                <td>
                    <label asp-for="Comments">Comments*:</label>
                </td>
                <td>
                    <textarea asp-for="Comments" rows="7" cols="40" style="width: 100%; max-width: 350px;"></textarea>
                </td>
            </tr>
            <tr>
                <td>
                    <div class="g-recaptcha" data-sitekey="6LeXGeAfAAAAAN9TPAVWCNdDcGNaoMryBMWwYI80"></div></td>
            </tr>
            <tr>
                <td>
                    <span class="float-start">* - required fields</span>
                </td>
                <td>
                    <button type="submit" class="btn btn-primary float-end">Submit</button>
                </td>
            </tr>
        </table>
    </form>
</div>