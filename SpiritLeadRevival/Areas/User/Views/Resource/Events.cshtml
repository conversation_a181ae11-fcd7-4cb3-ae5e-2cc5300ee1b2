@model IEnumerable<Event>
@{
    ViewData["Title"] = "Events";
}
@section SEO {
    <meta name="description" content="Events page for SpiritLead Revival."/>
}
<div class="container-fluid bg-light">
    <span class="h3 fw-bold mb-3"> We thank these organizations for their generous sponsorship of Spirit Lead Revival Ministries.</span>
    <div class="col-12 bg-light border rounded-3 p-3">
        <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3 pb-3">
            @foreach (var link in Model) {
                <div class="col">
                    <div class="card shadow-sm" style="">
                        <div class="card-header">
                            <h5 class="card-title">@link.Title</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">@Html.Raw(link.Description)</p>
                        </div>
                        <div class="card-footer">
                            <span class="h5">@link.Start - @link.End</span>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>