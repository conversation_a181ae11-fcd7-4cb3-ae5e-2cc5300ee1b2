@model IEnumerable<SpiritLeadRevival.Models.NewsItem>
@{
    ViewData["Title"] = "Church News";
}
<div class="bg-light border rounded-3 p-3 col-lg-12 col-md-12 my-3 pb-3">
    <h1 class="text-center mb-3 text-primary">
        Church News
    </h1>
    <div class="list-group">
        @foreach (var entry in Model) {
            <div class="card mb-3 rounded rounded-3">
                <div class="card-body">
                    <div class="card-title text-center">
                        <span class="text-center fw-bold">@entry.Title</span>
                    </div>
                    <div class="scroll">
                        <p class="card-text">@Html.Raw(entry.Content)</p>
                    </div>
                </div>
            </div>
        }
    </div>
</div>
