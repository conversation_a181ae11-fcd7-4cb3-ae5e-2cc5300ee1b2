@model IEnumerable<VideoFile>
@{
    ViewData["Title"] = "Video Library";
}
@section SEO {
    <meta name="description" content="Video Library page for SpiritLead Revival. Here you will find a archive of the videos that we have put out in the past."/>
}
<div class="row bg-light border rounded-3 px-3 mt-3">
    <div class="row text-center pt-3 pb-3">
            <h1 class="text-primary">Video Library</h1>
    </div>
    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-4">
        @foreach (var video in Model)
        {
            <div class="col">
                <div class="card shadow-sm">
                    <video controls class="ratio ratio-16x9">
                        <source src="@video.VideoUrl" />
                    </video>
                    <div class="card-body">
                        <h5 class="card-title">@video.Title</h5>
                        <p class="card-text">@video.Description</p>
                        <div class="d-flex justify-content-between align-items-center">
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>