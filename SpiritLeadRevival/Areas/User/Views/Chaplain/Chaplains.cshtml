@using SpiritLeadRevival.Utilities
@using Microsoft.AspNetCore.Mvc.TagHelpers
@model RoadChaplainVm
@{
    ViewData["Title"] = "Chaplains";
}
@section SEO {
    <meta name="description" content="Chaplain information page for SpiritLead Revival."/>
}
@*<div class="row bg-light rounded-3 mt-3">
    <div class="row text-center pt-3 pb-3">
        <a asp-area="User" asp-controller="RoadChaplain" asp-action="RoadChaplainNews"><h3>Chaplains News Corner</h3></a>
    </div>
</div>*@

<div class="row bg-light border rounded-3 px-3 mt-3 d-flex justify-content-center">
    <div class="row text-center pt-3 pb-3">
            <h1 class="text-primary">Meet Our Chaplains</h1>
    </div>
    <div class="row text-center py-3">
        <a asp-area="User" asp-controller="Chaplain" asp-action="ChaplainNews"><h3>Chaplain Corner (News and Videos)</h3></a>
    </div>
    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3 pb-3">
        @foreach (var description in Model.RoadChaplains) {
            <div class="col">
                <div class="card shadow-sm">
                    <div class="card-header text-center">
                        <h3>@description.FirstName @description.LastName</h3>
                    </div>
                    <img alt="@description.FirstName" src="@("../../" +description.ImageUrl)" style="max-height: 400px; object-fit: cover;" />
                    <div class="card-body">
                        @*<h5 class="card-title">@description.Name</h5>*@
                        <p class="card-text"><strong>Title:</strong> @description.Title</p>
                        <p class="card-text"><strong>Phone:</strong> <a href="@("tel:"+StaticUtilities.FormatPhoneNumber(description.PhoneNumber))"></a>@(StaticUtilities.FormatPhoneNumber(description.PhoneNumber))</p>
                        <p class="card-text"><strong>Email:</strong> <a href="@("mailto:"+description.Email)">@description.Email</a></p>

                        <div class="d-flex justify-content-between align-items-center">
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>
<div class="row">
    <div class="row text-center pt-3 pb-3">
        <h3>Becoming A Chaplain Introduction</h3>
    </div>
    <div class="row">
        <div class="card">
            <div class="card-header">
                Become a Chaplain
            </div>
            <div class="card-body">
                <p class="card-text">If you are interested in becoming a Chaplain please click the button below</p>
                <a asp-area="User" asp-controller="Chaplain" asp-action="BecomeAChaplain" class="btn btn-primary">Become a Chaplain</a>
            </div>
        </div>
    </div>
</div>