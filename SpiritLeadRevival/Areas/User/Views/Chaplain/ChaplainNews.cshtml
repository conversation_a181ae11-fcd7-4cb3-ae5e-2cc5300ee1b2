@model RoadChaplainNewsCornerVm
@{
    ViewData["Title"] = "Chaplain News Corner";
}
@section SEO {
    <meta name="description" content="News page for the Chaplains here at SpiritLead Revival."/>
}
<div class="row bg-light rounded-3 my-3 offset-lg-3 col-lg-6">
    <h1 class="text-primary text-center">Chaplains Corner</h1>
</div>
<div class="g-3 row">
    
    <div class="col bg-light rounded-3 mt-3">
        <div class="row text-center pt-3 pb-3">
            <h3>Chaplain News Corner</h3>
        </div>
        <div class="row g-3 mt-3 px-4 pb-3 rounded-3">
            @foreach (var entry in Model.RoadChaplainNews) {
                <div class="col">
                    <div class="card" style="max-height: 500px;">
                        <h5 class="card-header">Posted: @(entry.DatePosted.ToShortDateString())</h5>
                        <div class="card-body">
                            <h5 class="card-title">@entry.Title</h5>
                            <div class="scroll">
                                <p class="card-text">@Html.Raw(entry.Content)</p>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
    @* <div class="col bg-light rounded-3 mt-3">
        <div class="row text-center pt-3 pb-3">
            <h3>Chaplain Video Corner</h3>
        </div>
        <div class="row row-cols-1 row-cols-md-2 g-3 mt-3 px-4 pb-3 rounded-3">
            @foreach (var video in Model.RoadChaplainVideos) {
                <div class="col">
                    <div class="card shadow-sm">
                        <video controls class="ratio ratio-16x9">
                            <source src="@video.VideoUrl"/>
                        </video>
                        <div class="card-body">
                            <h5 class="card-title">@video.Title</h5>
                            <p class="card-text">@video.Description</p>
                            <div class="d-flex justify-content-between align-items-center">
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div> *@
</div>