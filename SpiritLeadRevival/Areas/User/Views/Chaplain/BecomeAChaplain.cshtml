@model BecomeAChaplainVm
@{
    ViewData["Title"] = "Becoming a Chaplain";
}
@section SEO {
    <meta name="description" content="Becoming a Chaplain page for SpiritLead Revival. If you have the drive to spread the word of God then consider becoming a Chaplain with us."/>
}
<div class="row">
    <div class="row w-100 text-center">
            <h1 class="text-primary">Become a Chaplain Program</h1>
    </div>
    @*<div class="col-6 col-md-12 text-center">
        <div class="row justify-content-evenly">
            <div class="card" style="width: 18rem;">
                <video controls class="pt-3 ratio ratio-16x9">
                    <source src="../../videos/RoadChaplainIntroduction/Welcome.mp4" />
                </video>
                <div class="card-body">
                    <h5 class="card-title">Welcome</h5>
                </div>
            </div>
            <div class="card" style="width: 18rem;">
                <video controls class="pt-3 ratio ratio-16x9">
                    <source src="../../videos/RoadChaplainIntroduction/QualitiesOfARoadChaplain.mp4" />
                </video>
                <div class="card-body">
                    <h5 class="card-title">Qualities of a Chaplain</h5>
                </div>
            </div>
        </div>
    </div>*@
</div>
<div class="row p-3">
    <div class="col-12 mb-3">
        <div class="card">
            <div class="card-body">@*
                <h5 class="card-title">Chaplain Introduction</h5>*@
                <p class="card-text">@Html.Raw(Model.RoadChaplainIntroduction.Introduction)</p>
            </div>
        </div>
    </div>
</div>
@* <div class="row row-cols-1 row-cols-md-2 g-3 mt-3 px-4 pb-3 rounded-3">
    @foreach (var video in Model.RoadChaplainIntroductionVideos) {
        <div class="col">
            <div class="card shadow-sm">
                <video controls class="ratio ratio-16x9">
                    <source src="@video.VideoUrl" />
                </video>
                <div class="card-body">
                    <h5 class="card-title">@video.Title</h5>
                    <p class="card-text">@video.Description</p>
                    <div class="d-flex justify-content-between align-items-center">
                    </div>
                </div>
            </div>
        </div>
    }
</div> *@
<div class="row row-cols-1 bg-light p-3 my-3">
    <div class="row">
        <span class="h3 text-center">Feel a calling to become a Chaplain?</span><br/>
        <p class="text-center">Please click on the link below to download the application!</p>
    </div>
    <div class="row">
        <a class="text-center" href="../../videos/RoadChaplainIntroduction/ChaplainApplication.pdf"><span class="h3">Chaplain Application</span></a>
    </div>

</div>