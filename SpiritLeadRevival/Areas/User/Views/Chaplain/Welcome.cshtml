@model BecomeAChaplainVm
@{
    ViewData["Title"] = "Chaplain Welcome Page";
}
@section SEO {
    <meta name="description" content="Events page for SpiritLead Revival."/>
}
<div class="row">
    <div class="row w-100 text-center">
        <span class="h3">Welcome to the Chaplain Program</span>
    </div>
    <div class="col-6 col-md-12 text-center">
        <div class="row justify-content-evenly">
            <div class="card" style="width: 18rem;">
                <video controls src="../../RoadChaplainIntroduction/Welcome.mp4" class="pt-3 ratio ratio-16x9"></video>
                <div class="card-body">
                    <h5 class="card-title">Welcome</h5>
                </div>
            </div>
            <div class="card" style="width: 18rem;">
                <video controls src="../../RoadChaplainIntroduction/QualitiesOfARoadChaplain.mp4" class="pt-3 ratio ratio-16x9"></video>
                <div class="card-body">
                    <h5 class="card-title">Qualities of a Chaplain</h5>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row p-3">
    <div class="col-12 mb-3">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Chaplain Introduction</h5>
                <p class="card-text">@Html.Raw(Model.RoadChaplainIntroduction.Introduction)</p>
            </div>
        </div>
    </div>
</div>
@* <div class="row row-cols-1 row-cols-md-2 g-3 mt-3 px-4 pb-3 rounded-3">
    @foreach (var video in Model.RoadChaplainIntroductionVideos) {
        <div class="col">
            <div class="card shadow-sm">
                <video controls class="ratio ratio-16x9">
                    <source src="@video.VideoUrl" />
                </video>
                <div class="card-body">
                    <h5 class="card-title">@video.Title</h5>
                    <p class="card-text">@video.Description</p>
                    <div class="d-flex justify-content-between align-items-center">
                    </div>
                </div>
            </div>
        </div>
    }
</div> *@
<div class="row bg-light p-3 my-3">
    <div class="row">
        <span class="h3">Feel a calling to become a Chaplain?</span><br/>
        <p class="">Please click on the link below to download the application!</p>
    </div>
    <div class="row">
        <a href="../../RoadChaplainIntroduction/ChaplainApplication.pdf"><span class="h3">Chaplain Application</span></a>
    </div>

</div>