using AspNetSeo.CoreMvc;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Models.ViewModels;
using MimeKit;
using MailKit.Net.Smtp;
using Microsoft.AspNetCore.Identity.UI.Services;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Areas.User.Controllers;
[Area("User")]
public class ServicesController : Controller {
    private readonly IUnitOfWork _unitOfWork;
    private readonly IConfiguration _config;
    private readonly IEmailSender _emailSender;
    [BindProperty] private ServiceVm ServiceVm { get; set; }

    public ServicesController(IUnitOfWork unitOfWork, IConfiguration config, IEmailSender emailSender) {
        _unitOfWork = unitOfWork;
        _config = config;
        _emailSender = emailSender;
    }
    [PageTitle("Services Landing Page")]
    [MetaDescription("Landing page for Services information")]
    public IActionResult Index() {
        ServiceVm = new ServiceVm() {
            MinistryHelpLinks = _unitOfWork.MinistryHelpLink.GetAll(),
            MinistryDescriptions = _unitOfWork.MinistryDescription.GetAll(),
        };
        return View(ServiceVm);
    }
    [PageTitle("Help Links")]
    [MetaDescription("A list of help links")]
    public IActionResult HelpLinks() {
        var objFromDb = _unitOfWork.MinistryHelpLink.GetAll();
        return View(objFromDb);
    }
    [PageTitle("Prayer Request")]
    [MetaDescription("Prayer Request form")]
    public IActionResult PrayerRequest()
    {
        var objFromDb = _unitOfWork.PraiseReport.GetTopNumberOfResults(3);
        var viewModel = new PrayerRequestVm()
        {
            Request = default!,
            PraiseReports = objFromDb
        };
        return View(viewModel);
    }

    [HttpPost]
    public IActionResult PrayerRequest(PrayerRequestVm model) {
        var recipients = _unitOfWork.PrayerRequestRecipient.GetAll();

        if (ModelState.IsValid) {
            var message = new MimeMessage();
            message.ReplyTo.Add(new MailboxAddress(model.Request.FromName, model.Request.FromEmail));
            message.From.Add(new MailboxAddress("Prayer Request", _config["Email:username"]));
            foreach (var recipient in recipients) {
                message.To.Add(new MailboxAddress(recipient.Name, recipient.Email));
            }
            message.Subject = model.Request.Subject;
            message.Body = new TextPart("plain") { Text = model.Request.Body.ToString() + $"\n Prayer request from: {model.Request.FromEmail}" };

            using (var client = new SmtpClient()) {
                client.ServerCertificateValidationCallback = (s, c, h, e) => true;
                client.Connect(_config["Email:Server"], Convert.ToInt32(_config["Email:Port"]?.ToString()));
                client.Authenticate(_config["Email:username"], _config["Email:password"]);
                client.Send(message);
                client.Disconnect(true);
            }
            return RedirectToAction("Index");
        }

        // ReSharper disable once Mvc.ViewNotResolved
        return View();
    }
    [PageTitle("Revivals")]
    [MetaDescription("Description and list of revival events")]
    public IActionResult Revivals() {
        RevivalVm revivalVm = new RevivalVm() {
            Conferences = _unitOfWork.RevivalConference.GetAll(),
            References = _unitOfWork.RevivalReference.GetAll(),
            SpiritLeadDecisions = _unitOfWork.RevivalSpiritLeadDecision.GetAll(),
            PrincipalOfFaiths = _unitOfWork.RevivalPrincipalOfFaith.GetAll()
        };
        return View(revivalVm);
    }


}