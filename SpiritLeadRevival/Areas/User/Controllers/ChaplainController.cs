using AspNetSeo.CoreMvc;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models.ViewModels;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Areas.User.Controllers {
    [Area("User")]
    public class ChaplainController : Controller {
        private readonly IUnitOfWork _unitOfWork;
        public ChaplainController(IUnitOfWork unitOfWork) {
            _unitOfWork = unitOfWork;
        }
        [PageTitle("Chaplain Home")]
        [MetaDescription("Landing page for all Chaplain Information")]
        public IActionResult ChaplainNews() {

            var objFromDb = new RoadChaplainNewsCornerVm() {
                RoadChaplainNews = _unitOfWork.RoadChaplainNews.GetLast(1),
                RoadChaplainVideos = _unitOfWork.RoadChaplainVideo.GetAll()
            };
            return View(objFromDb);
        }

        [PageTitle("Chaplains")]
        [MetaDescription("List of Church Chaplains")]
        public IActionResult Chaplains() {
            var roadChaplainList = _unitOfWork.RoadChaplain.GetAll().ToList();
            var filterList = from chaplain in roadChaplainList
                orderby chaplain.DisplayOrder
                select chaplain;
            var objFromDb = new RoadChaplainVm() {
                RoadChaplains = filterList,
            };
            return View(objFromDb);
        }
        [PageTitle("Become a Chaplain")]
        [MetaDescription("Page Describing the process of becoming a Chaplain")]
        public IActionResult BecomeAChaplain() {
            var objFromDb = new BecomeAChaplainVm() {
                RoadChaplainIntroduction = _unitOfWork.RoadChaplainIntroduction.GetFirstOrDefault(x => x.Id == 1),
                RoadChaplainIntroductionVideos = _unitOfWork.RoadChaplainIntroductionVideo.GetAll()
            };
            return View(objFromDb);
        }

        //public IActionResult Welcome() {
        //    var objFromDb = new BecomeARoadChaplainVM() {
        //        RoadChaplainIntroduction = _unitOfWork.RoadChaplainIntroduction.GetFirstOrDefault(x => x.Id == 1),
        //        RoadChaplainIntroductionVideos = _unitOfWork.RoadChaplainIntroductionVideo.GetAll()
        //    };
        //    return View(objFromDb);
        //}
    }
}
