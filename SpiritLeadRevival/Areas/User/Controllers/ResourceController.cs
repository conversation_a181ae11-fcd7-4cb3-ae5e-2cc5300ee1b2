using AspNetSeo.CoreMvc;
using MailKit.Net.Smtp;
using Microsoft.AspNetCore.Mvc;
using MimeKit;
using SpiritLeadRevival.Models.ViewModels;
using SpiritLeadRevival.Repositories.Repository.IRepository;


namespace SpiritLeadRevival.Areas.User.Controllers
{
    [Area("User")]
    public class ResourceController : Controller
    {
        private AboutUsViewModel _aboutUsViewModel;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IConfiguration _config;
        public ResourceController(IUnitOfWork unitOfWork, IConfiguration config) {
            _unitOfWork = unitOfWork;
            _config = config;
        }
        [PageTitle("Church News")]
        [MetaDescription("Page for the Latest Church News")]
        public IActionResult ChurchNews()
        {
            var objFromDb = _unitOfWork.NewsItem.GetAll();
            if (objFromDb is null) return NotFound();
            return View(objFromDb);
        }

        [PageTitle("Contact Us")]
        [MetaDescription("Please Contact Us")]
        public IActionResult ContactUs()
        {
            return View();
        }

        //POST
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult ContactUs(ContactVm obj)
        {
            var email = new MimeMessage();
            email.From.Add(new MailboxAddress("Site Contact Request",_config["Email:username"]));
            email.To.Add(new MailboxAddress("Olen Thompson",_config["Email:username"]));
            email.ReplyTo.Add(new MailboxAddress(obj.Name, obj.FromEmail));
            email.Subject = "Site Contact Request";
            var fromEmailLine = $"Site Contact request from: \n{obj.Name}({obj.FromEmail})\n\n";
            if (obj.PhoneNumber != null || obj.PhoneNumber != String.Empty) {
                var message = fromEmailLine + obj.Comments + "<br/>PhoneNumber: " + obj.PhoneNumber;
                email.Body = new TextPart(MimeKit.Text.TextFormat.Html) { Text = message };
            }
            else
            {
                var message = fromEmailLine + obj.Comments;
                email.Body = new TextPart(MimeKit.Text.TextFormat.Html) { Text = message };
            }

            using (var emailClient = new SmtpClient())
            {
                emailClient.ServerCertificateValidationCallback = (s, c, h, e) => true;
                emailClient.Connect(_config["Email:Server"], Convert.ToInt32(_config["Email:Port"]));
                emailClient.Authenticate(_config["Email:Username"], _config["Email:Password"]);
                emailClient.Send(email);
                emailClient.Disconnect(true);
            }

            return RedirectToAction("Index","Home");

        }
        [PageTitle("About Us")]
        [MetaDescription("About Us Page")]
        public IActionResult AboutUs()
        {
            _aboutUsViewModel = new AboutUsViewModel() {
                Pastors = _unitOfWork.Pastors.GetAll(),                
                BoardMembers = _unitOfWork.BoardMembers.GetAll(),
                StaffMembers = _unitOfWork.StaffMembers.GetAll()
            };
            return View(_aboutUsViewModel);
        }
        [PageTitle("Ministry Sponsors")]
        [MetaDescription("List of Ministry Sponsors")]
        public IActionResult Sponsors()
        {
            var objFromDb = _unitOfWork.Sponsor.GetAll();
            return View(objFromDb);
        }
        [PageTitle("Reading List")]
        [MetaDescription("Read the new testament in a year")]
        public IActionResult ReadingList()
        {
            return View();
        }
        [PageTitle("Video Library")]
        [MetaDescription("The Video Library for the church")]
        public IActionResult VideoLibrary()
        {
            return View(_unitOfWork.VideoFile.GetAll());
        }
        [PageTitle("Church Events")]
        [MetaDescription("Church Events")]
        public IActionResult Events()
        {
            return View(_unitOfWork.Event.GetAll());
        }

        [PageTitle("Vision")]
        [MetaDescription("Ministry Vision")]
        public IActionResult Vision()
        {
            return View();
        }
    }
}
