using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Models.ViewModels;
using System.Diagnostics;
using AspNetSeo.CoreMvc;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Areas.User.Controllers
{
    [Area("User")]
    public class HomeController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<HomeController> _logger;
        [BindProperty]
        // ReSharper disable once InconsistentNaming
        private HomeVm HomeVM { get; set; }


        public HomeController(ILogger<HomeController> logger, IUnitOfWork unitOfWork)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
        }
        [PageTitle("Home")]
        [MetaDescription("Homepage for SpiritLead Revival Ministries.")]
        public IActionResult Index()
        {
            HomeVM = new HomeVm()
            {
                CarouselItems = _unitOfWork.CarouselItem.GetAll(),
                NewsItems = _unitOfWork.NewsItem.GetLatest(),
                VideoFiles = _unitOfWork.VideoFile.GetTopNumberResults(1),
                DailyScripture = _unitOfWork.DailyScripture.GetFirstOrDefault(x => x.Id == DateTime.Now.DayOfYear),
                Events = _unitOfWork.Event.GetTopNumberResults(1),
            };
            return View(HomeVM);
        }

        [PageTitle("Subscribe")]
        public IActionResult Subscribe()
        {
            return View("Subscribe");
        }

        [HttpPost, ActionName("Create")]
        [ValidateAntiForgeryToken]
        public IActionResult Create(MailingListContact obj)
        {
            _unitOfWork.MailingList.Add(obj);
            _unitOfWork.Save();
            return RedirectToAction("Index");
        }

        [PageTitle("Error page")]
        [MetaDescription("Error page")]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}