@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.RoadChaplainIntroduction
<form method="post" asp-action="Create">
    <div class="border p-3 mt-4">
        <div class="row pb-2">
            <h2 class="text-primary">Create Introduction Entry</h2>
            <hr />
        </div>
        <div class="mb-3">
            <label asp-for="Id"></label>
            <input asp-for="Id" class="form-control" disabled />
            <span asp-validation-for="Id" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="Introduction"></label>
            <textarea asp-for="Introduction" class="form-control" rows="10"></textarea>
            <span asp-validation-for="Introduction" class="text-danger"></span>
        </div>
        <button type="submit" class="btn btn-primary" style="width:150px">Create</button>
        <a asp-controller="ChaplainIntroduction" asp-action="Index" class="btn btn-secondary" style="width:150px">
            Back to List
        </a>
    </div>
</form>

@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
    <script src="https://cdn.tiny.cloud/1/1clodgnpnexp5xs3lgo0vcgcm6av7k5yqfeha2nh8avohao9/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script>
    <script>
        tinymce.init({
            selector: 'textarea',
            plugins: 'advlist autolink lists link image charmap print preview hr anchor pagebreak',
            toolbar_mode: 'floating',
        });
    </script>
}