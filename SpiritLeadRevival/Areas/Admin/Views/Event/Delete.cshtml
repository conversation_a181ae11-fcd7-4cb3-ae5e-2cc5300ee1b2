@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.Event

<form method="post" asp-action="Delete">
    <input asp-for="Id" hidden />
    <div class="border p-3 mt-4">
        <div class="row pb-2">
            <h2 class="text-primary">Delete Event</h2>
            <hr />
        </div>
        <div class="mb-3">
            <label asp-for="Title"></label>
            <input asp-for="Title" class="form-control" disabled />
            <span asp-validation-for="Title" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="Description"></label>
            <textarea asp-for="Description" class="form-control" disabled ></textarea>
            <span asp-validation-for="Description" class="text-danger"></span>
        </div>
        <div class="row g-3">
            <div class="mb-3 col-12 col-md-6">
                <label asp-for="Start"></label>
                <input asp-for="Start" class="form-control" id="startTime" disabled />
                <span asp-validation-for="Start" class="text-danger"></span>
            </div>
            <div class="mb-3 col-12 col-md-6">
                <label asp-for="End"></label>
                <input asp-for="End" class="form-control" id="endTime" disabled />
                <span asp-validation-for="End" class="text-danger"></span>
            </div>
        </div>
        <button type="submit" class="btn btn-danger" style="width:150px">Delete</button>
        <a asp-controller="Event" asp-action="Index" class="btn btn-secondary" style="width:150px">
            Back to List
        </a>
    </div>
</form>

@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
        <script src="https://cdn.tiny.cloud/1/1clodgnpnexp5xs3lgo0vcgcm6av7k5yqfeha2nh8avohao9/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script>
        <script>
            tinymce.init({
                selector: 'textarea',
                plugins: 'advlist autolink lists link image charmap print preview hr anchor pagebreak',
                toolbar_mode: 'floating',
            });
        </script>
        
    }
}