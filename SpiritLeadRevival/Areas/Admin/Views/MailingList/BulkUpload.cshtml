@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.MailingListContact
@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}
<form method="post" asp-action="BulkUpload">
    <div class="mb-3">
        <label for="nameInput" class="form-label">Name</label>
        <input type="text" class="form-control" id="nameInput" placeholder="Name"/>
    </div>
    <div class="mb-3">
        <label for="emailInput" class="form-label">Email</label>
        <input type="email" class="form-control" id="emailInput" placeholder="<EMAIL>" />
    </div>
    <button type="submit" class="btn btn-primary" style="width:150px">Create</button>
    <a asp-controller="MailingList" asp-action="Index" class="btn btn-secondary" style="width:150px">
        Back to List
    </a>
</form>