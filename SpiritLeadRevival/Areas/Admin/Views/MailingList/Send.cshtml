@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.MailingListEmail

<div class="container">
    <form asp-controller="MailingList" asp-action="Send" asp-route-message="title" class="bg-light mb-3">
        <div class="border p-3 mt-4">
            <div class="row pb-2">
                <h2 class="text-primary">Send Mailing</h2>
                <hr/>
            </div>
            <div class="mb-3">
                <label for="MessageTitle" class="form-label">Email Title</label>
                <input id="MessageTitle" class="form-control" name="subject" value="Church Update" />
            </div>
            <div class="mb-3">
                <label for="Content" class="form-label">Body of the message</label>
                <textarea id="Content" class="form-control" name="message" rows="10"></textarea>
            </div>
            <button type="submit" class="btn btn-primary" style="width:150px">Send</button>
            <a asp-controller="MailingList" asp-action="Index" class="btn btn-secondary" style="width:150px">
                Back to List
            </a>
        </div>
    </form></div>

@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
        <script src="https://cdn.tiny.cloud/1/1clodgnpnexp5xs3lgo0vcgcm6av7k5yqfeha2nh8avohao9/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script>
        <script>
    tinymce.init({
      selector: 'textarea',
      plugins: 'advlist autolink lists link image charmap print preview hr anchor pagebreak',
      toolbar_mode: 'floating'
    });
  </script>
    }
}