@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.MailingListContact

<form method="post" asp-action="Delete">
    <input asp-for="Id" hidden />
    <div class="border p-3 mt-4">
        <div class="row pb-2">
            <h2 class="text-primary">Delete Mailing List Entry</h2>
            <hr />
        </div>
        <div class="mb-3">
            <label asp-for="Name"></label>
            <input asp-for="Name" class="form-control" disabled />
            <span asp-validation-for="Name" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="EmailAddress"></label>
            <input asp-for="EmailAddress" class="form-control" disabled />
            <span asp-validation-for="EmailAddress" class="text-danger"></span>
        </div>
        <button type="submit" class="btn btn-danger" style="width:150px">Delete</button>
        <a asp-controller="MailingList" asp-action="Index" class="btn btn-secondary" style="width:150px">
            Back to List
        </a>
    </div>
</form>

@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}