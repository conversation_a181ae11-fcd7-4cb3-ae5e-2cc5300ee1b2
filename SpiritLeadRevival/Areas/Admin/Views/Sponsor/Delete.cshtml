@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.Sponsor

<form method="post" asp-action="Delete">
    <input asp-for="Id" hidden />
    <div class="row">
        <div class="col-10">
            <div class="border p-3 mt-4">
                <div class="row pb-2">
                    <h2 class="text-primary">Delete Ministry Help Link</h2>
                    <hr />
                </div>
                <div class="mb-3">
                    <label asp-for="Name"></label>
                    <input asp-for="Name" disabled class="form-control" />
                </div>
                <div class="mb-3">
                    <label asp-for="Description"></label>
                    <textarea asp-for="Description" disabled class="form-control" rows="4"></textarea>
                </div>
                <button type="submit" class="btn btn-danger" style="width: 150px">Delete</button>
                <a asp-controller="Sponsor" asp-action="Index" class="btn btn-secondary" style="width: 150px">
                    Back to List
                </a>
            </div>
        </div>
        <div class="col-2 pt-4">
            <img src="../../../@Model.ImageUrl"
                 width="100%" style="border-radius: 5px; border: 1px solid #bbb9b9" />
        </div>
    </div>
</form>
@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
        <script src="https://cdn.tiny.cloud/1/1clodgnpnexp5xs3lgo0vcgcm6av7k5yqfeha2nh8avohao9/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script>
        <script>
            tinymce.init({
                selector: 'textarea',
                plugins: 'advlist autolink lists link image charmap print preview hr anchor pagebreak',
                toolbar_mode: 'floating',
            });
        </script>
    }
}