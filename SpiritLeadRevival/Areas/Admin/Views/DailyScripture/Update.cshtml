@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.DailyScripture

<form method="post">
    <input asp-for="Id" hidden/>
    <div class="border p-3 mt-4">
        <div class="row pb-2">
            <h2 class="text-primary">Edit Scripture</h2>
            <hr/>
        </div>
        <div class="mb-3">
            <label asp-for="Id"></label>
            <input asp-for="Id" class="form-control" disabled />
            <span asp-validation-for="Id" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="Verse"></label>
            <input asp-for="Verse" class="form-control"/>
            <span asp-validation-for="Verse" class="text-danger"></span>
        </div>
        <button type="submit" class="btn btn-primary" style="width: 150px">Edit</button>
        <a asp-controller="NewsItem" asp-action="Index" class="btn btn-secondary" style="width: 150px">
            Back to List
        </a>
    </div>
</form>

@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}