@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.RoadChaplain

<form method="post" asp-action="Delete">
    <input asp-for="Id" hidden />
    <div class="row">
        <div class="col-10">
            <div class="border p-3 mt-4">
                <div class="row pb-2">
                    <h2 class="text-primary">Delete Chaplain</h2>
                    <hr />
                </div>
                <div class="mb-3">
                    <label asp-for="FirstName"></label>
                    <input asp-for="FirstName" disabled class="form-control" />
                </div>
                
                <div class="mb-3">
                    <label asp-for="LastName"></label>
                    <input asp-for="LastName" disabled class="form-control" />
                </div>
                <div class="mb-3">
                    <label asp-for="DisplayOrder"></label>
                    <input asp-for="DisplayOrder" disabled class="form-control" />
                </div>
                <button type="submit" class="btn btn-danger" style="width: 150px">Delete</button>
                <a asp-controller="Chaplain" asp-action="Index" class="btn btn-secondary" style="width: 150px">
                    Back to List
                </a>
            </div>
        </div>
        <div class="col-2 pt-4">
            <img src="../../@Model.ImageUrl"
                 width="100%" style="border-radius: 5px; border: 1px solid #bbb9b9" />
        </div>
    </div>
</form>
@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}