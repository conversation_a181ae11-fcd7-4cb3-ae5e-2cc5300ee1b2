@{
    ViewData["Title"] = "Admin Dashboard";
    ViewData["BreadcrumbItems"] = new List<dynamic>
    {
        new { Text = "Dashboard", Url = "", IsActive = true }
    };
}

<div class="row">
    <div class="col-12">
        <div class="admin-card">
            <div class="admin-card-header">
                <h1 class="admin-card-title">
                    <i class="bi bi-speedometer2 me-2"></i>
                    Admin Dashboard
                </h1>
            </div>
            <div class="admin-card-body">
                <div class="row">
                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">@ViewBag.NewsItemCount</h4>
                                        <p class="card-text">News Items</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-newspaper display-4"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <a asp-controller="NewsItem" asp-action="Index" class="text-white text-decoration-none">
                                    <small>View Details <i class="bi bi-arrow-right"></i></small>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">@ViewBag.CarouselItemCount</h4>
                                        <p class="card-text">Carousel Items</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-images display-4"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <a asp-controller="CarouselItem" asp-action="Index" class="text-white text-decoration-none">
                                    <small>View Details <i class="bi bi-arrow-right"></i></small>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">@ViewBag.EventCount</h4>
                                        <p class="card-text">Events</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-calendar-event display-4"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <a asp-controller="Event" asp-action="Index" class="text-white text-decoration-none">
                                    <small>View Details <i class="bi bi-arrow-right"></i></small>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">@ViewBag.MailingListCount</h4>
                                        <p class="card-text">Mailing List</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-envelope display-4"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <a asp-controller="MailingList" asp-action="Index" class="text-white text-decoration-none">
                                    <small>View Details <i class="bi bi-arrow-right"></i></small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12">
                        <h3>Quick Actions</h3>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <a asp-controller="NewsItem" asp-action="Create" class="btn btn-primary btn-lg w-100">
                                    <i class="bi bi-plus-circle me-2"></i>
                                    Add News Item
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a asp-controller="CarouselItem" asp-action="Upcert" class="btn btn-success btn-lg w-100">
                                    <i class="bi bi-plus-circle me-2"></i>
                                    Add Carousel Item
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a asp-controller="Event" asp-action="Create" class="btn btn-info btn-lg w-100">
                                    <i class="bi bi-plus-circle me-2"></i>
                                    Add Event
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12">
                        <h3>Recent Activity</h3>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            Welcome to the modernized admin panel! This dashboard provides quick access to your most important content management tools.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
