@using Microsoft.AspNetCore.Mvc.TagHelpers
@model IEnumerable<SpiritLeadRevival.Models.MinistryDescription>

<div class="container p-3">
    <div class="row pt-4">
        <div class="col-6">
            <h2 class="text-primary">Ministry Description List</h2>
        </div>
        <div class="col-6 text-end">
            <a asp-controller="MinistryDescription" asp-action="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> &nbsp; Create Ministry Description
            </a>
        </div>
    </div>
    <br /><br />

    <table class="table table-bordered table-striped" style="width:100%">
        <thead>
        <tr>
            <th>Name</th>
            <th>Description</th>
        </tr>
        </thead>
        <tbody>
        @foreach(var obj in Model)
        {
            <tr>
                <td width="15%">
                    @obj.Name
                </td>
                <td>
                    @Html.Raw(obj.Description)
                </td>
                <td width="25%">
                    <div class="btn-group" role="group">
                        <a asp-controller="MinistryDescription" asp-action="Update" asp-route-id="@obj.Id"
                           class="btn btn-primary mx-2"> <i class="bi bi-pencil-square"></i> Edit</a>
                        <a asp-controller="MinistryDescription" asp-action="Delete" asp-route-id="@obj.Id"
                           class="btn btn-danger mx-2"> <i class="bi bi-trash-fill"></i> Delete</a>
                    </div>
                </td>
            </tr>
        }
        </tbody>

    </table>
</div>