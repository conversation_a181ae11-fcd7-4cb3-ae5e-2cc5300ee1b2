@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.Pastor
<form method="post" asp-action="Upcert" enctype="multipart/form-data">
    <input asp-for="@Model.Id" hidden />

    <div class="row">
        <div class="col-10">
            <div class="border p-3 mt-4 row">
                <div class="col-12 pb-2">
                    <h2 class="text-primary">@(Model.Id==0 ?"Create": "Update")  Pastor</h2>
                    <hr />
                </div>
                <div class="mb-3">
                    <label asp-for="@Model.Name"></label>
                    <input asp-for="@Model.Name" class="form-control" />
                    <span asp-validation-for="@Model.Name" class="text-danger"></span>
                </div>
                <div class="mb-3">
                    <label asp-for="@Model.Title"></label>
                    <input asp-for="@Model.Title" class="form-control" />
                    <span asp-validation-for="@Model.Title" class="text-danger"></span>
                </div>
                <div class="mb-3">
                    <label asp-for="@Model.Biography"></label>
                    <textarea asp-for="@Model.Biography" class="form-control" id="mce"></textarea>
                    <span asp-validation-for="@Model.Biography" class="text-danger"></span>
                </div>
                <div class="mb-3">
                    <label asp-for="@Model.ImageUrl"></label>
                    <input asp-for="@Model.ImageUrl" class="form-control" type="file" name="file"/>
                    <span asp-validation-for="@Model.ImageUrl" class="text-danger"></span>
                </div>
                <div class="col-12">
                    @if (Model.Id != 0)
                    {
                        <button type="submit" class="btn btn-primary" style="width:150px">Update</button>
                    }
                    else
                    {
                        <button type="submit" onclick="return ValidateInput()" class="btn btn-primary" style="width:150px">Create</button>
                    }
                    <a asp-controller="PastorInformation" asp-action="Index" class="btn btn-secondary" style="width:150px">
                        Back to List
                    </a>
                </div>

            </div>
        </div>
        <div class="col-2 pt-4">
            <img src="../../@Model.ImageUrl" width="100%" style="border-radius:5px; border:1px solid #bbb9b9" />
        </div>
    </div>

	





</form>

@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
    <link href="~/js/tinymce/skins/content/SpiritLead/content.css"/>
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.tiny.cloud/1/1clodgnpnexp5xs3lgo0vcgcm6av7k5yqfeha2nh8avohao9/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script>
    <script>
        tinymce.init({
            selector: '#mce',
            plugins: 'advlist autolink lists link charmap preview hr anchor pagebreak',
            //skin: 'SpiritLead',
            toolbar_mode: 'floating',
            content_css: '~/js/tinymce/skins/content/SpiritLead/content.css',
            //skin_url:'~/js/tinymce/skins/content'
                });
        function ValidateInput(){
            if(document.getElementById("file").value==""){
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Please upload an Image!',
                });
                return false;
            }
            return true;
        }
    </script>
}