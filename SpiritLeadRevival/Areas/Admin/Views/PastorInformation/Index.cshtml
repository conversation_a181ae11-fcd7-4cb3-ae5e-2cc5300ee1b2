@using Microsoft.AspNetCore.Mvc.TagHelpers
@model IEnumerable<SpiritLeadRevival.Models.Pastor>
<div class="container p-3">
    <div class="row pt-4">
        <div class="col-6">
            <h2 class="text-primary">Pastor List</h2>
        </div>
        <div class="col-6 text-end">
            <a class="btn btn-primary" asp-controller="PastorInformation" asp-action="Upcert">
                <i class="bi bi-plus-circle"></i> &nbsp; Create New Pastor
            </a>
        </div>
    </div>
    <br /><br />

    <table class="table table-bordered table-striped" style="width:100%">
        <thead>
        <tr>
            <th>Name</th>
            <th>Title</th>
            <th>Biography</th>
            <th>Image</th>
            <th>Actions</th>
            <th></th>
        </tr>
        </thead>
        <tbody>
        @foreach(var obj in Model)
        {
            <tr>
                <td>@obj.Name</td>
                <td>@obj.Title</td>
                <td>@Html.Raw(obj.Biography)</td>
                <td><img src="../../@obj.ImageUrl" width="100%" style="max-height: 350px; object-fit: scale-down;" /></td>
                <td width="15%">
                    <div class="btn-group" role="group">
                        <a asp-controller="PastorInformation" asp-action="Upcert" asp-route-id="@obj.Id"
                           class="btn btn-primary mx-2"> <i class="bi bi-pencil-square"></i> Edit</a>
                        <a asp-controller="PastorInformation" asp-action="Delete" asp-route-id="@obj.Id"
                           class="btn btn-danger mx-2"> <i class="bi bi-trash-fill"></i> Delete</a>
                    </div>
                </td>
            </tr>
        }
        </tbody>
    </table>
</div>