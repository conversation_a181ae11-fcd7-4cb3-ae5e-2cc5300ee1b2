@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.RevivalSpiritLeadDecision

<form method="post">
    <div class="border p-3 mt-4">
        <div class="row pb-2">
            <h2 class="text-primary">Create Decision Entry</h2>
            <hr />
        </div>
        <div class="mb-3">
            <label asp-for="EventDateTime"></label>
            <input asp-for="EventDateTime" type="datetime-local" class="form-control" />
            <span asp-validation-for="EventDateTime" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="City"></label>
            <input asp-for="City" class="form-control" />
            <span asp-validation-for="City" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="State"></label>
            <input asp-for="State" class="form-control" />
            <span asp-validation-for="State" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="Description"></label>
            <textarea asp-for="Description" class="form-control" ></textarea>
            <span asp-validation-for="Description" class="text-danger"></span>
        </div>
        <button type="submit" class="btn btn-primary" style="width:150px">Create</button>
        <a asp-controller="RevivalSpiritLeadDecision" asp-action="Index" class="btn btn-secondary" style="width:150px">
            Back to List
        </a>
    </div>
</form>

@section Scripts{
    @{
    <partial name="_ValidationScriptsPartial" />
    <script src="https://cdn.tiny.cloud/1/1clodgnpnexp5xs3lgo0vcgcm6av7k5yqfeha2nh8avohao9/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script>
        <script>
        tinymce.init({
            selector: 'textarea',
            plugins: 'advlist autolink lists link image charmap print preview hr anchor pagebreak',
            toolbar_mode: 'floating',
        });
    </script>
    }
}