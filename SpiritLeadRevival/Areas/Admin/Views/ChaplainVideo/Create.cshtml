@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.RoadChaplainVideo
@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
        
    }
}
<form method="post" enctype="multipart/form-data" asp-action="Create">
    <div class="border p-3 mt-4">
        <div class="row pb-2">
            <h2 class="text-primary">Create Video Entry</h2>
            <hr />
        </div>
        <div class="mb-3">
            <label asp-for="Title"></label>
            <input asp-for="Title" class="form-control" />
            <span asp-validation-for="Title" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="Description"></label>
            <input asp-for="Description" class="form-control" />
            <span asp-validation-for="Description" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label for="files" class="form-label">Default file input example</label>
            <input class="form-control" type="file" id="files" name="files" onchange="uploadFiles('files');">
        </div>
        <button type="submit" class="btn btn-primary" style="width:150px">Create</button>
        <a asp-controller="ChaplainVideo" asp-action="Index" class="btn btn-secondary" style="width:150px">
            Back to List
        </a>
    </div>
</form>

