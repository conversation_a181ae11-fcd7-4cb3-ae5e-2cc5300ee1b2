@using Microsoft.AspNetCore.Mvc.TagHelpers
@model IEnumerable<SpiritLeadRevival.Models.RoadChaplainVideo>

<div class="container p-3">
    <div class="row pt-4">
        <div class="col-6">
            <h2 class="text-primary">Video List</h2>
        </div>
        <div class="col-6 text-end">
            <a asp-controller="ChaplainVideo" asp-action="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Create New Video
            </a>
        </div>
    </div>
    <br /><br />

    <table class="table table-bordered table-striped" style="width:100%">
        <thead>
        <tr>
            <th>
                Title
            </th>
            <th width="50%">
                Image
            </th>
            <th>Description</th>
            <th></th>
        </tr>
        </thead>
        <tbody>
        @foreach(var obj in Model)
        {
            <tr>
                <td>
                    @obj.Title
                </td>
                <td>
                    <video src="@obj.VideoUrl" controls
                         width="100%" style="border-radius: 5px; border: 1px solid #bbb9b9" />
                </td>
                <td>
                    @obj.Description
                </td>
                <td width="15%">
                    <div class="btn-group" role="group">
                       @* <a asp-controller="VideoFile" asp-action="Edit" asp-route-id="@obj.Id" disabled
                           class="btn btn-primary mx-2"> <i class="bi bi-pencil-square"></i> Edit</a>*@
                        <a asp-controller="ChaplainVideo" asp-action="Delete" asp-route-id="@obj.Id"
                           class="btn btn-danger mx-2"> <i class="bi bi-trash-fill"></i> Delete</a>
                    </div>
                </td>
                
                
            </tr>
        }
        </tbody>

    </table>
</div>