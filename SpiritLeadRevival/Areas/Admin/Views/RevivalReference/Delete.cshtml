@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.RevivalReference

<form method="post" asp-action="Delete">
    <input asp-for="Id" hidden />
    <div class="border p-3 mt-4">
        <div class="row pb-2">
            <h2 class="text-primary">Delete Reference</h2>
            <hr />
        </div>
        <div class="mb-3">
            <label asp-for="Writer"></label>
            <input asp-for="Writer" disabled class="form-control" />
        </div>
        <div class="mb-3">
            <label asp-for="Letter"></label>
            <input asp-for="Letter" disabled class="form-control" />
        </div>
        <button type="submit" class="btn btn-danger" style="width:150px">Delete</button>
        <a asp-controller="RevivalReference" asp-action="Index" class="btn btn-secondary" style="width:150px">
            Back to List
        </a>
    </div>
</form>

@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}