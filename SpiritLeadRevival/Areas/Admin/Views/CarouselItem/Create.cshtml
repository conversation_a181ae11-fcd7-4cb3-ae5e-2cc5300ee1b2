@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.CarouselItem

<form method="post">
    <div class="border p-3 mt-4">
        <div class="row pb-2">
            <h2 class="text-primary">Create Carousel</h2>
            <hr />
        </div>
        <div class="mb-3">
            <label asp-for="Caption"></label>
            <input asp-for="Caption" class="form-control" />
            <span asp-validation-for="Caption" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="ImageUrl"></label>
            <input asp-for="ImageUrl" class="form-control" />
            <span asp-validation-for="ImageUrl" class="text-danger"></span>
        </div>
        <button type="submit" class="btn btn-primary" style="width:150px">Create</button>
        <a asp-controller="CarouselItem" asp-action="Index" class="btn btn-secondary" style="width:150px">
            Back to List
        </a>
    </div>
</form>

@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}