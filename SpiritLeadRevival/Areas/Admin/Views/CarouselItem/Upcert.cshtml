@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.CarouselItem
<form method="post" asp-action="Upcert" enctype="multipart/form-data">
	<input asp-for="@Model.Id" hidden />

    <div class="row">
		<div class="col-10">
			<div class="border p-3 mt-4 row">
		<div class="col-12 pb-2">
			<h2 class="text-primary">@(Model.Id==0 ?"Create": "Update")  Carousel</h2>
			<hr />
		</div>
		@*<div asp-validation-summary="All"></div>*@
		<div class="mb-3">
			<label asp-for="@Model.Caption"></label>
			<input asp-for="@Model.Caption" class="form-control" />
			<span asp-validation-for="@Model.Caption" class="text-danger"></span>
		</div>

        <div class="mb-3">
            <label for="formFile" class="form-label">Default file input example</label>
            <input class="form-control" type="file" name="file" id="file">
        </div>
        <div class="col-12">
			@if (Model.Id != 0)
			{
				<button type="submit" class="btn btn-primary" style="width:150px">Update</button>
			}
			else
			{
				<button type="submit" onclick="return ValidateInput()" class="btn btn-primary" style="width:150px">Create</button>
			}
			<a asp-controller="CarouselItem" asp-action="Index" class="btn btn-secondary" style="width:150px">
				Back to List
			</a>
		</div>

	</div>
		</div>
		<div class="col-2 pt-4">
			<img src="../../@Model.ImageUrl"
			width="100%" style="border-radius:5px; border:1px solid #bbb9b9" />
		</div>
	</div>

	





</form>

@section Scripts{
	@{
	<partial name="_ValidationScriptsPartial" />
	}
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
	    function ValidateInput(){
		    if(document.getElementById("file").value==""){
			    Swal.fire({
			      icon: 'error',
			      title: 'Oops...',
			      text: 'Please upload an Image!',
			    });
			    return false;
		    }
		    return true;
	    }
    </script>
}