@using Microsoft.AspNetCore.Mvc.TagHelpers
@model IEnumerable<SpiritLeadRevival.Models.CarouselItem>

<div class="container p-3">
    <div class="row pt-4">
        <div class="col-6">
            <h2 class="text-primary">Carousel List</h2>
        </div>
        <div class="col-6 text-end">
            <a asp-controller="CarouselItem" asp-action="Upcert" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> &nbsp; Create New Carousel
            </a>
        </div>
    </div>
    <br /><br />

    <table class="table table-bordered table-striped" style="width:100%">
        <thead>
        <tr>
            <th>
                Caption
            </th>
            <th>
                Image
            </th>
            <th>Actions</th>
            <th></th>
        </tr>
        </thead>
        <tbody>
        @foreach(var obj in Model)
        {
            <tr>
                <td width="50%">
                    @obj.Caption
                </td>
                <td>
                    <img src="../../@obj.ImageUrl"
                         width="100%" style="border-radius: 5px; border: 1px solid #bbb9b9" />
                </td>
                <td width="25%">
                    <div class="btn-group" role="group">
                        <a asp-controller="CarouselItem" asp-action="Upcert" asp-route-id="@obj.Id"
                           class="btn btn-primary mx-2"> <i class="bi bi-pencil-square"></i> Edit</a>
                        <a asp-controller="CarouselItem" asp-action="Delete" asp-route-id="@obj.Id"
                           class="btn btn-danger mx-2"> <i class="bi bi-trash-fill"></i> Delete</a>
                    </div>
                </td>
                
                
            </tr>
        }
        </tbody>

    </table>
</div>