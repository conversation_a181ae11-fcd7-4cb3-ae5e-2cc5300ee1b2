@using Microsoft.AspNetCore.Mvc.TagHelpers
@model IEnumerable<SpiritLeadRevival.Models.CarouselItem>

@{
    ViewData["Title"] = "Carousel Management";
    ViewData["BreadcrumbItems"] = new List<dynamic>
    {
        new { Text = "Content", Url = "#", IsActive = false },
        new { Text = "Carousel", Url = "", IsActive = true }
    };
}

<div class="admin-card">
    <div class="admin-card-header d-flex justify-content-between align-items-center">
        <h1 class="admin-card-title">
            <i class="bi bi-images me-2"></i>
            Carousel Management
        </h1>
        <div class="admin-card-actions">
            <a asp-controller="CarouselItem" asp-action="Upcert" class="btn btn-light">
                <i class="bi bi-plus-circle"></i> Add New Item
            </a>
        </div>
    </div>

    <div class="admin-card-body">
        @if (Model.Any())
        {
            <div class="admin-table-container">
                <table class="table admin-table">
                    <thead>
                        <tr>
                            <th>Caption</th>
                            <th>Image</th>
                            <th class="no-sort">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach(var obj in Model)
                        {
                            <tr>
                                <td data-label="Caption">
                                    <div class="fw-semibold">@obj.Caption</div>
                                </td>
                                <td data-label="Image">
                                    <img src="../../@obj.ImageUrl"
                                         class="img-thumbnail"
                                         style="max-width: 120px; max-height: 80px; object-fit: cover;"
                                         alt="@obj.Caption" />
                                </td>
                                <td data-label="Actions">
                                    <div class="admin-actions">
                                        <a asp-controller="CarouselItem" asp-action="Upcert" asp-route-id="@obj.Id"
                                           class="btn btn-sm btn-primary" title="Edit">
                                            <i class="bi bi-pencil-square"></i>
                                            <span class="d-none d-sm-inline">Edit</span>
                                        </a>
                                        <a asp-controller="CarouselItem" asp-action="Delete" asp-route-id="@obj.Id"
                                           class="btn btn-sm btn-danger" title="Delete">
                                            <i class="bi bi-trash-fill"></i>
                                            <span class="d-none d-sm-inline">Delete</span>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="bi bi-images display-1 text-muted"></i>
                <h3 class="mt-3 text-muted">No carousel items found</h3>
                <p class="text-muted">Get started by creating your first carousel item.</p>
                <a asp-controller="CarouselItem" asp-action="Upcert" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Create First Item
                </a>
            </div>
        }
    </div>
</div>