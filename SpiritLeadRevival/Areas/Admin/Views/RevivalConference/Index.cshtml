@using Microsoft.AspNetCore.Mvc.TagHelpers
@model IEnumerable<SpiritLeadRevival.Models.RevivalConference>

<div class="container p-3">
    <div class="row pt-4">
        <div class="col-6">
            <h2 class="text-primary">Revival List</h2>
        </div>
        <div class="col-6 text-end">
            <a asp-controller="RevivalConference" asp-action="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> &nbsp; Create New Conference
            </a>
        </div>
    </div>
    <br /><br />

    <table class="table table-bordered table-striped" style="width:100%">
        <thead>
        <tr>
            <th>Description</th>
            <th>City</th>
            <th>State</th>
            <th>Time(converted to your time)</th>
            <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        @foreach(var obj in Model)
        {
            <tr>
                <td>
                    <div class="scroll">
                        @Html.Raw(obj.Description)
                    </div>
                </td>
                <td>
                    @obj.City
                </td>
                <td>@obj.State</td>
                <td>@obj.EventDateTime.ToLocalTime()</td>
                <td>
                    <div class="btn-group" role="group">
                        <a asp-controller="RevivalConference" asp-action="Edit" asp-route-id="@obj.Id" class="btn btn-primary mx-2"> <i class="bi bi-pencil-square"></i> Edit</a>
                        <a asp-controller="RevivalConference" asp-action="Delete" asp-route-id="@obj.Id" class="btn btn-danger mx-2"> <i class="bi bi-trash-fill"></i> Delete</a>
                    </div>
                </td>
            </tr>
        }
        </tbody>

    </table>
</div>