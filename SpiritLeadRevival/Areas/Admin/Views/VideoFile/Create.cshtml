@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.VideoFile
@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
        
    }
}
<form method="post" enctype="multipart/form-data" asp-action="Create">
    <div class="border p-3 mt-4">
        <div class="row pb-2">
            <h2 class="text-primary">Create Video Entry</h2>
            <hr />
        </div>
        <div class="mb-3">
            <label asp-for="Title"></label>
            <input asp-for="Title" class="form-control" />
            <span asp-validation-for="Title" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="Description"></label>
            <input asp-for="Description" class="form-control" />
            <span asp-validation-for="Description" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label for="files" class="form-label">Default file input example</label>
            <input class="form-control" type="file" id="files" name="files" onchange="uploadFiles('files');">
        </div>
        <hr/>
        <div id="progress" class="progress" style="width:250px">
            <div id="bar" class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">
                0%
            </div>
        </div>

        
        <button type="submit" class="btn btn-primary" style="width:150px" onclick="uploadFiles('files')">Create</button>
        <a asp-controller="VideoFile" asp-action="Index" class="btn btn-secondary" style="width:150px">
            Back to List
        </a>
        <span id="upload-status" style="display:none">Files uploaded.</span>
    </div>
</form>

