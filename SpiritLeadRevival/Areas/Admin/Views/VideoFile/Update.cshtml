@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.VideoFile

<form method="post" asp-action="Update">
    <input asp-for="Id" hidden />
    <div class="row">
        <div class="col-8">
            <div class="border p-3 mt-4">
                <div class="row pb-2">
                    <h2 class="text-primary">Update Video</h2>
                    <hr />
                </div>
                <input hidden asp-for="VideoUrl"/>
                <div class="mb-3">
                    <label asp-for="Title"></label>
                    <input asp-for="Title" class="form-control" />
                </div>
                <div class="mb-3">
                    <label asp-for="Description"></label>
                    <input asp-for="Description" class="form-control" />
                </div>
                <button type="submit" class="btn btn-danger" style="width: 150px">Update</button>
                <a asp-controller="VideoFile" asp-action="Index" class="btn btn-secondary" style="width: 150px">
                    Back to List
                </a>
            </div>
        </div>
        <div class="col-4 pt-4">
            <video src="@Model.VideoUrl" controls
                 width="100%" style="border-radius: 5px; border: 1px solid #bbb9b9" />
        </div>
    </div>
</form>
@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}