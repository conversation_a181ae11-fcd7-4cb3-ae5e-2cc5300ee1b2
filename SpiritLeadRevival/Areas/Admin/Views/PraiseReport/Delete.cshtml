@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.PraiseReport

<form method="post" asp-action="Delete">
    <input asp-for="Id" hidden />
    <div class="border p-3 mt-4">
        <div class="row pb-2">
            <h2 class="text-primary">Delete Praise Entry</h2>
            <hr />
        </div>
        <div class="mb-3">
            <label asp-for="Title"></label>
            <input asp-for="Title" disabled class="form-control" />
        </div>
        <div class="mb-3">
            <label asp-for="Content"></label>
            <input asp-for="Content" disabled class="form-control" />
        </div>
        <button type="submit" class="btn btn-danger" style="width:150px">Delete</button>
        <a asp-controller="PraiseReport" asp-action="Index" class="btn btn-secondary" style="width:150px">
            Back to List
        </a>
    </div>
</form>

@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}