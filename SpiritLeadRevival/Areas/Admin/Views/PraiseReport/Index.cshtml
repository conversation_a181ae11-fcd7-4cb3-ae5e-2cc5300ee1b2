@using Microsoft.AspNetCore.Mvc.TagHelpers
@model IEnumerable<PraiseReport>

<div class="container p-3">
    <div class="row pt-4">
        <div class="col-6">
            <h2 class="text-primary">Praise Report List</h2>
        </div>
        <div class="col-6 text-end">
            <a asp-controller="PraiseReport" asp-action="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> &nbsp; Create New Praise Report
            </a>
        </div>
    </div>
    <br /><br />

    <table class="table table-bordered table-striped" style="width:100%">
        <thead>
        <tr>
            <th>
                Title
            </th>
            <th>
                Content
            </th>
            <th>Date Posted</th>
            <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        @foreach(var obj in Model)
        {
            <tr>
                <td width="25%">
                    @obj.Title
                </td>
                <td width="50%">
                    @Html.Raw(obj.Content)
                </td>
                <td width="15%">@obj.DatePosted</td>
                <td>
                    <div class="btn-group" role="group">
                        <a asp-controller="PraiseReport" asp-action="Edit" asp-route-id="@obj.Id"
                           class="btn btn-primary mx-2"> <i class="bi bi-pencil-square"></i> Edit</a>
                        <a asp-controller="PraiseReport" asp-action="Delete" asp-route-id="@obj.Id"
                           class="btn btn-danger mx-2"> <i class="bi bi-trash-fill"></i> Delete</a>
                    </div>
                </td>
            </tr>
        }
        </tbody>

    </table>
</div>