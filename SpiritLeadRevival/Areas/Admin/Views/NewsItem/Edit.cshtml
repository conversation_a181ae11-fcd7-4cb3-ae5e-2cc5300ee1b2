@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.NewsItem

<form method="post">
    <input asp-for="Id" hidden/>
    <div class="border p-3 mt-4">
        <div class="row pb-2">
            <h2 class="text-primary">Edit News Item</h2>
            <hr/>
        </div>
        <div class="mb-3">
            <label asp-for="Title"></label>
            <input asp-for="Title" class="form-control"/>
            <span asp-validation-for="Title" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="Content"></label>
            <textarea asp-for="Content" class="form-control" rows="10"></textarea>
            <span asp-validation-for="Content" class="text-danger"></span>
        </div>
        <button type="submit" class="btn btn-primary" style="width: 150px">Edit</button>
        <a asp-controller="NewsItem" asp-action="Index" class="btn btn-secondary" style="width: 150px">
            Back to List
        </a>
    </div>
</form>

@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
        <script src="https://cdn.tiny.cloud/1/1clodgnpnexp5xs3lgo0vcgcm6av7k5yqfeha2nh8avohao9/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script>
        <script>
    tinymce.init({
      selector: 'textarea',
      plugins: 'advlist autolink lists link image charmap print preview hr anchor pagebreak',
      toolbar_mode: 'floating',
    });
  </script>
    }
}