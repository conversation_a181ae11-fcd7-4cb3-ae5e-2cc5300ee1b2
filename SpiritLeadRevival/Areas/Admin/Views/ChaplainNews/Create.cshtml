@using Microsoft.AspNetCore.Mvc.TagHelpers
@model RoadChaplainNews
<form method="post" asp-action="Create">
    <div class="border p-3 mt-4">
        <div class="row pb-2">
            <h2 class="text-primary">Create Chaplain News Entry</h2>
            <hr />
        </div>
        <div class="mb-3">
            <label asp-for="Title"></label>
            <input asp-for="Title" class="form-control" />
            <span asp-validation-for="Title" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="@Model.Content"></label>
            <textarea id="mce" asp-for="@Model.Content" rows="6" class="form-control"></textarea>
            <span asp-validation-for="@Model.Content" class="text-danger"></span>
        </div>
        <button type="submit" class="btn btn-primary" style="width:150px">Create</button>
        <a asp-controller="ChaplainNews" asp-action="Index" class="btn btn-secondary" style="width:150px">
            Back to List
        </a>
    </div>
</form>
@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
        <script src="https://cdn.tiny.cloud/1/1clodgnpnexp5xs3lgo0vcgcm6av7k5yqfeha2nh8avohao9/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script>
        <script>
            tinymce.init({
                selector: 'textarea',
                plugins: 'advlist autolink lists link image charmap print preview hr anchor pagebreak',
                toolbar_mode: 'floating',
            });
        </script>
    }
}