@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.PrayerRequestRecipient

<form method="post" asp-action="Delete">
    <input asp-for="Id" hidden />
    <div class="row">
        <div class="col-10">
            <div class="border p-3 mt-4">
                <div class="row pb-2">
                    <h2 class="text-primary">Delete Recipient</h2>
                    <hr />
                </div>
                <div class="mb-3">
                    <label asp-for="Name"></label>
                    <input asp-for="Name" disabled class="form-control" />
                </div>
                <button type="submit" class="btn btn-danger" style="width: 150px">Delete</button>
                <a asp-controller="PrayerRequestRecipient" asp-action="Index" class="btn btn-secondary" style="width: 150px">
                    Back to List
                </a>
            </div>
        </div>
    </div>
</form>
@section Scripts{
    @{
    <partial name="_ValidationScriptsPartial" />
    }
}