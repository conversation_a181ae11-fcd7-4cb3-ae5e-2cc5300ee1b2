@using Microsoft.AspNetCore.Mvc.TagHelpers
@model IEnumerable<SpiritLeadRevival.Models.PrayerRequestRecipient>

<div class="container p-3">
    <div class="row pt-4">
        <div class="col-6">
            <h2 class="text-primary">Recipient List</h2>
        </div>
        <div class="col-6 text-end">
            <a asp-controller="PrayerRequestRecipient" asp-action="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> &nbsp; Create New Recipient
            </a>
        </div>
    </div>
    <br /><br />

    <table class="table table-bordered table-striped" style="width:100%">
        <thead>
        <tr>
            <th>
                Name
            </th>
            <th>
                Email
            </th>
            <th>Actions</th>
            <th></th>
        </tr>
        </thead>
        <tbody>
        @foreach(var obj in Model)
        {
            <tr>
                <td width="50%">
                    @obj.Name
                </td>
                <td>
                    @obj.Email
                </td>
                <td width="25%">
                    <div class="btn-group" role="group">
                        <a asp-controller="PrayerRequestRecipient" asp-action="Edit" asp-route-id="@obj.Id"
                           class="btn btn-primary mx-2"> <i class="bi bi-pencil-square"></i> Edit</a>
                        <a asp-controller="PrayerRequestRecipient" asp-action="Delete" asp-route-id="@obj.Id"
                           class="btn btn-danger mx-2"> <i class="bi bi-trash-fill"></i> Delete</a>
                    </div>
                </td>
                
                
            </tr>
        }
        </tbody>

    </table>
</div>