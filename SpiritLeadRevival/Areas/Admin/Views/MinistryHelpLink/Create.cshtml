@using Microsoft.AspNetCore.Mvc.TagHelpers
@model SpiritLeadRevival.Models.MinistryHelpLink

<form method="post">
    <div class="border p-3 mt-4">
        <div class="row pb-2">
            <h2 class="text-primary">Create Ministry Help Link</h2>
            <hr />
        </div>
        <div class="mb-3">
            <label asp-for="Name"></label>
            <input asp-for="Name" class="form-control" />
            <span asp-validation-for="Name" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="Description"></label>
            <input asp-for="Description" class="form-control" />
            <span asp-validation-for="Description" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="Url"></label>
            <input asp-for="Url" class="form-control" />
            <span asp-validation-for="Url" class="text-danger"></span>
        </div>
        <div class="mb-3">
            <label asp-for="ImageUrl"></label>
            <input asp-for="ImageUrl" type="file" class="form-control" />
            <span asp-validation-for="ImageUrl" class="text-danger"></span>
        </div>
        <button type="submit" class="btn btn-primary" style="width:150px">Create</button>
        <a asp-controller="MinistryHelpLink" asp-action="Index" class="btn btn-secondary" style="width:150px">
            Back to List
        </a>
    </div>
</form>

@section Scripts{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}