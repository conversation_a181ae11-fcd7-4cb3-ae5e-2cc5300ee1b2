using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin)]
    public class PastorInformationController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public PastorInformationController(IWebHostEnvironment webHostEnvironment, IUnitOfWork unitOfWork)
        {
            _webHostEnvironment = webHostEnvironment;
            _unitOfWork = unitOfWork;
        }
        public IActionResult Index()
        {
            var objList = _unitOfWork.Pastors.GetAll();
            return View(objList);
        }
        [HttpGet] 
        public IActionResult Upcert(int? id)
        {
            if (id == null || id == 0)
            {
                var roadChaplain = new Pastor();
                return View(roadChaplain);
            }
            else
            {
                var objFromDb = _unitOfWork.Pastors.GetFirstOrDefault(x => x.Id == id);
                return View(objFromDb);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Upcert(Pastor obj, IFormFile? file)
        {
            if (!ModelState.IsValid) return View(obj);
            var wwwRootPath = StaticUtilities.GetCurrentDirectory();

            if (file != null)
            {
                var fileName = Guid.NewGuid();
                var uploads = Path.Combine(wwwRootPath, @"images\Pastors");
                var extenstion = Path.GetExtension(file.FileName);

                if (obj.ImageUrl is { } or "")
                {
                    var oldImagePath = Path.Combine(wwwRootPath, obj.ImageUrl.TrimStart('\\'));
                    if (System.IO.File.Exists(oldImagePath))
                    {
                        System.IO.File.Delete(oldImagePath);
                    }
                }

                using (var fileStream =
                       new FileStream(Path.Combine(uploads, fileName + extenstion), FileMode.Create))
                {
                    file.CopyTo(fileStream);
                }

                obj.ImageUrl = @"images/pastors/" + fileName + extenstion;
            }

            if (file == null && obj.Id != 0)
            {

            }

            if (obj.Id == 0)
            {
                _unitOfWork.Pastors.Add(obj);
            }
            else
            {
                var old = _unitOfWork.Pastors.GetFirstOrDefaultWithNoTracking(x => x.Id == obj.Id);
                obj.ImageUrl = old.ImageUrl;
                _unitOfWork.Pastors.Update(obj);
            }
            _unitOfWork.Save();
            TempData["success"] = "Staff Member added/created successfully";
            return RedirectToAction("Index");
        }

        public IActionResult Delete(int? id)
        {
            if (id == null || id == 0)
            {
                return NotFound();
            }

            var fromDb = _unitOfWork.Pastors.GetFirstOrDefault(x => x.Id == id);
            if (fromDb == null)
            {
                return NotFound();
            }

            return View(fromDb);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeletePost(int? id)
        {
            var obj = _unitOfWork.Pastors.GetFirstOrDefault(x => x.Id == id);
            if (obj is null)
            {
                return NotFound();
            }

            // Attempt to delete associated file if it exists
            if (!string.IsNullOrEmpty(obj.ImageUrl))
            {
                try
                {
                    var webRootPath = StaticUtilities.GetCurrentDirectory();
                    var filePath = Path.Combine(webRootPath, obj.ImageUrl.TrimStart('/', '\\'));
                    if (System.IO.File.Exists(filePath))
                    {
                        System.IO.File.Delete(filePath);
                    }
                }
                catch (Exception e)
                {
                    // Log the error but don't prevent the database deletion
                    Console.WriteLine($"Error deleting file for Pastor {id}: {e.Message}");
                    // Could also log to a proper logging framework here
                }
            }

            _unitOfWork.Pastors.Remove(obj);
            _unitOfWork.Save();
            TempData["success"] = "Pastor Deleted";
            return RedirectToAction("Index");
        }
    }
}
