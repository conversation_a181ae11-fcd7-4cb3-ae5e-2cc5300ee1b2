using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin + ", " + StaticDetails.RoleRoadChaplain)]

    public class ChaplainNewsController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;

        public ChaplainNewsController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        [HttpGet]
        public IActionResult Index()
        {
            var objFromDb = _unitOfWork.RoadChaplainNews.GetAll();
            return View(objFromDb);
        }
        [HttpGet]
        public IActionResult Create()
        {
            return View();
        }
        [HttpPost, ActionName("Create")]
        [ValidateAntiForgeryToken]
        public IActionResult Create(RoadChaplainNews obj)
        {
            if (ModelState.IsValid)
            {
                obj.DatePosted = DateTime.Now;
                _unitOfWork.RoadChaplainNews.Add(obj);
                _unitOfWork.Save();
                return RedirectToAction("Index");
            }

            return View(obj);
        }
        [HttpGet]
        public IActionResult Update(int? id)
        {
            if (id is null or 0) return NotFound();
            var objFromDb = _unitOfWork.RoadChaplainNews.GetFirstOrDefault(x => x.Id == id);
            if (objFromDb is null) return NotFound();
            objFromDb.DatePosted = DateTime.Now;
            return View(objFromDb);
        }
        [HttpPost, ActionName("Update")]
        [ValidateAntiForgeryToken]
        public IActionResult Update(RoadChaplainNews obj)
        {
            if (ModelState.IsValid)
            {
                
                _unitOfWork.RoadChaplainNews.Update(obj);
                _unitOfWork.Save();
                return RedirectToAction("Index");
            }

            return View(obj);
        }
        [HttpGet]
        public IActionResult Delete(int? id)
        {
            if (id is null or 0) return NotFound();
            var objFromDb = _unitOfWork.RoadChaplainNews.GetFirstOrDefault(x => x.Id == id);
            if (objFromDb is null) return NotFound();
            return View(objFromDb);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult Delete(RoadChaplainNews obj)
        {
            if (!ModelState.IsValid) return View(obj);
            _unitOfWork.RoadChaplainNews.Remove(obj);
            _unitOfWork.Save();
            return RedirectToAction("Index");

        }
    }
}
