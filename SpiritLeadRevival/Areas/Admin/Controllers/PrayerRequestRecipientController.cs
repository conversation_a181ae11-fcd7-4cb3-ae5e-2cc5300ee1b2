using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin)]
    public class PrayerRequestRecipientController : Controller {
        private readonly IUnitOfWork _unitOfWork;
        public PrayerRequestRecipientController(IUnitOfWork unitOfWork) {
            _unitOfWork = unitOfWork;
        }

        public IActionResult Index() {
            var objFromDb = _unitOfWork.PrayerRequestRecipient.GetAll();
            return View(objFromDb);
        }

        [HttpGet]
        public IActionResult Create() {
            return  View();
        }

        //POST
        [HttpPost, ActionName("Create")]
        [ValidateAntiForgeryToken]
        public IActionResult Create(PrayerRequestRecipient obj) {
            _unitOfWork.PrayerRequestRecipient.Add(obj);
            _unitOfWork.Save();
            return RedirectToAction("Index");
        }

        public IActionResult Edit() {
            return View();
        }

        [HttpPost, ActionName("Edit")]
        [ValidateAntiForgeryToken]
        public IActionResult Edit(PrayerRequestRecipient obj) {
            _unitOfWork.PrayerRequestRecipient.Update(obj);
            _unitOfWork.Save();
            return RedirectToAction("Index");
        }

        public IActionResult Delete()
        {
            return View();
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult Delete(PrayerRequestRecipient obj)
        {
            _unitOfWork.PrayerRequestRecipient.Remove(obj);
            _unitOfWork.Save();
            return RedirectToAction("Index");
        }
    }
}
