using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin)]
    public class ChaplainIntroductionController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;

        public ChaplainIntroductionController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public IActionResult Index()
        {
            var objFromDb = _unitOfWork.RoadChaplainIntroduction.GetAll();
            return View(objFromDb);
        }
        [HttpGet]
        public IActionResult Create()
        {
            return View();
        }

        //POST
        [HttpPost, ActionName("Create")]
        [ValidateAntiForgeryToken]
        public IActionResult Create(RoadChaplainIntroduction obj)
        {
            if (obj != null)
            {
                _unitOfWork.RoadChaplainIntroduction.Add(obj);
                _unitOfWork.Save();
                TempData["success"] = "Introduction Added!";
                return RedirectToAction("Index");
            }
            return View(obj);
        }

        [HttpGet]
        public IActionResult Update(int? id)
        {
            var objFromDb = _unitOfWork.RoadChaplainIntroduction.GetFirstOrDefault(x => x.Id == id);
            return View(objFromDb);
        }

        [HttpPost, ActionName("Update")]
        [ValidateAntiForgeryToken]
        public IActionResult UpdatePost(RoadChaplainIntroduction obj)
        {
            if (ModelState.IsValid)
            {
                _unitOfWork.RoadChaplainIntroduction.Update(obj);
                _unitOfWork.Save();
                TempData["success"] = "Introduction Updated!";
                return RedirectToAction("Index");
            }

            return View(obj);
        }

        public IActionResult Delete(int? id)
        {
            var objFromDb = _unitOfWork.RoadChaplainIntroduction.GetFirstOrDefault(x => x.Id == id);
            return View(objFromDb);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeletePost(RoadChaplainIntroduction obj)
        {
            if (obj != null)
            {
                _unitOfWork.RoadChaplainIntroduction.Remove(obj);
                _unitOfWork.Save();
                TempData["success"] = "Introduction Removed!";
                return RedirectToAction("Index");
            }

            // ReSharper disable once Mvc.ViewNotResolved
            return View(obj);
        }
    }
}
