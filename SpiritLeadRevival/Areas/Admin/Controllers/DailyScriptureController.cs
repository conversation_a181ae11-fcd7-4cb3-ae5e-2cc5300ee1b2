using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin)]
    public class DailyScriptureController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;

        public DailyScriptureController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public IActionResult Index()
        {
            IEnumerable<DailyScripture> dailyScripturesFromDb = _unitOfWork.DailyScripture.GetAll();
            return View(dailyScripturesFromDb);
        }
        [HttpGet]
        public IActionResult Create()
        {
            return View();
        }

        //POST
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Create(DailyScripture obj)
        {
            if (obj != null)
            {
                _unitOfWork.DailyScripture.Add(obj);
                _unitOfWork.Save();
                TempData["success"] = "Daily Verse Added!";
                return RedirectToAction("Index");
            }
            return View(obj);
        }

        [HttpGet]
        [HttpGet]
        public IActionResult Update(int? id)
        {
            var objFromDb = _unitOfWork.DailyScripture.GetFirstOrDefault(x => x.Id == id);
            return View(objFromDb);
        }

        [HttpPost, ActionName("Update")]
        [ValidateAntiForgeryToken]
        public IActionResult UpdatePost(DailyScripture obj)
        {
            if (ModelState.IsValid)
            {
                _unitOfWork.DailyScripture.Update(obj);
                _unitOfWork.Save();
                TempData["success"] = "Daily Verse Updated!";
                return RedirectToAction("Index");
            }

            return View(obj);
        }

        [HttpGet]
        public IActionResult Delete(int? id)
        {
            var objFromDb = _unitOfWork.DailyScripture.GetFirstOrDefault(x => x.Id == id);
            return View(objFromDb);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeletePost(DailyScripture obj)
        {
            if (obj != null)
            {
                _unitOfWork.DailyScripture.Remove(obj);
                _unitOfWork.Save();
                TempData["success"] = "Daily Scripture Removed!";
                return RedirectToAction("Index");
            }

            // ReSharper disable once Mvc.ViewNotResolved
            return View(obj);
        }
    }
}
