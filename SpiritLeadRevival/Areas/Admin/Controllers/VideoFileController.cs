using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin)]
    public class VideoFileController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;
        private IWebHostEnvironment _webHost;
        private int IProgress { get; set; }
        private const string ProgressSessionKey = "FileUploadProgress";

        
        

        public VideoFileController(IUnitOfWork unitOfWork, IWebHostEnvironment webHost) {
            _unitOfWork = unitOfWork;
            _webHost = webHost;
        }
        public IActionResult Index()
        {
            IEnumerable<VideoFile> videosFromDb = _unitOfWork.VideoFile.GetAll();
            return View(videosFromDb);
        }

        public IActionResult Update(int? id) {
            if (id == null) return NotFound();
            var objFromDb = _unitOfWork.VideoFile.GetFirstOrDefault(x => x.Id == id);
            if (objFromDb == null) return NotFound();
            else return View(objFromDb);

        }

        [HttpPost, ActionName("Update")]
        [ValidateAntiForgeryToken]
        public IActionResult Update(VideoFile obj) {
            if (ModelState.IsValid) {
                _unitOfWork.VideoFile.Update(obj);
                _unitOfWork.Save();
            }
            return RedirectToAction("Index");
        }
        [HttpGet]
        public IActionResult Create(int? id)
        {
            if (id == null || id == 0)
            {
                var videoFile = new VideoFile();
                return View(videoFile);
            }
            else
            {
                var videoFile = _unitOfWork.VideoFile.GetFirstOrDefault(x => x.Id == id);
                return View(videoFile);
            }
        }

        [HttpPost, ActionName("Create")]
        [ValidateAntiForgeryToken]
        [RequestFormLimits(MultipartBodyLengthLimit = StaticDetails.FileSizeLimit500MB)]
        public async Task<IActionResult> Create(VideoFile obj, IList<IFormFile> files) {
            SetProgress(HttpContext.Session, 0);
            if (ModelState.IsValid)
            {
                IProgress = 0;
                long totalBytes = files.Sum(x => x.Length);

                foreach (IFormFile file in files) {
                    ContentDispositionHeaderValue contentDispositionHeaderValue =
                        ContentDispositionHeaderValue.Parse(file.ContentDisposition);

                    string fileName = contentDispositionHeaderValue.FileName.ToString().Trim('"');

                    fileName = EnsureCorrectFilename(fileName);

                    obj.VideoUrl = GetDbPathAndFilename(fileName);

                    byte[] buffer = new byte[(500 * 1024) * 1024];

                    using (FileStream output = System.IO.File.Create(GetPathAndFilename(fileName)))
                    {
                        using (Stream input = file.OpenReadStream()) {
                            long totalReadBytes = 0;
                            int readBytes;

                            while ((readBytes = await input.ReadAsync(buffer, 0, buffer.Length)) > 0) {
                                await output.WriteAsync(buffer, 0, readBytes);
                                totalReadBytes += readBytes;
                                int progress = (int)((float)totalReadBytes / (float)totalBytes * 100);
                                SetProgress(HttpContext.Session, progress);
                                await Task.Delay(100);
                            }
                        }
                    }
                }
                _unitOfWork.VideoFile.Add(obj);
                _unitOfWork.Save();
            }
            return RedirectToAction("Index");
        }
        private static void SetProgress(ISession session, int progress) {
            session.SetInt32(ProgressSessionKey, progress);
        }
        private static int GetProgress(ISession session) {
            int? progress = session.GetInt32(ProgressSessionKey);

            if (progress.HasValue)
                return progress.Value;

            return 0;
        }

        [HttpPost]
        public ActionResult Progress() {
            int progress = GetProgress(HttpContext.Session);
            return Content(progress.ToString());
        }
        [HttpGet]
        public IActionResult Delete(int? id)
        {
            if (id == null || id == 0)
            {
                return NotFound();
            }

            var objFromDb = _unitOfWork.VideoFile.GetFirstOrDefault(x => x.Id == id);
            if (objFromDb == null)
            {
                return NotFound();
            }

            return View(objFromDb);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeletePost(int? id)
        {
            var obj = _unitOfWork.VideoFile.GetFirstOrDefault(x => x.Id == id);
            if (obj is null)
            {
                return NotFound();
            }

            // Attempt to delete associated file if it exists
            if (!string.IsNullOrEmpty(obj.VideoUrl))
            {
                try
                {
                    var webRootPath = StaticUtilities.GetCurrentDirectory();
                    var filePath = Path.Combine(webRootPath, obj.VideoUrl.TrimStart('/', '\\'));
                    if (System.IO.File.Exists(filePath))
                    {
                        System.IO.File.Delete(filePath);
                    }
                }
                catch (Exception e)
                {
                    // Log the error but don't prevent the database deletion
                    Console.WriteLine($"Error deleting file for VideoFile {id}: {e.Message}");
                    // Could also log to a proper logging framework here
                }
            }

            _unitOfWork.VideoFile.Remove(obj);
            _unitOfWork.Save();
            TempData["success"] = "Video Deleted";
            return RedirectToAction("Index");
        }

        

        private string EnsureCorrectFilename(string filename)
        {
            if (filename.Contains("\\"))
                filename = filename.Substring(filename.LastIndexOf("\\", StringComparison.Ordinal) + 1);

            return filename;
        }

        private string GetPathAndFilename(string filename)
        {
            string path = StaticUtilities.GetCurrentDirectory() + "\\videos\\";

            if (!Directory.Exists(path))
                Directory.CreateDirectory(path);

            return path + filename;
        }

        private string GetDbPathAndFilename(string fileName)
        {
            string path = "/videos/";

            return path + fileName;
        }
    }
}
