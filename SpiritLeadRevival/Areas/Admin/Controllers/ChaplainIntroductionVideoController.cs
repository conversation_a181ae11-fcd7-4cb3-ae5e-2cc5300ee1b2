using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin)]
    public class ChaplainIntroductionVideoController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;

        private int IProgress { get; set; }


        public ChaplainIntroductionVideoController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public IActionResult Index()
        {
            IEnumerable<RoadChaplainIntroductionVideo> videosFromDb = _unitOfWork.RoadChaplainIntroductionVideo.GetAll();
            return View(videosFromDb);
        }

        [HttpGet]
        public IActionResult Create(int? id)
        {
            if (id == null || id == 0)
            {
                var videoFile = new RoadChaplainIntroductionVideo();
                return View(videoFile);
            }
            else
            {
                var videoFile = _unitOfWork.RoadChaplainIntroductionVideo.GetFirstOrDefault(x => x.Id == id);
                return View(videoFile);
            }
        }

        [HttpPost, ActionName("Create")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(RoadChaplainIntroductionVideo obj, List<IFormFile> files)
        {
            if (ModelState.IsValid) {
                this.IProgress = 0;
                long totalBytes = files.Sum(x => x.Length);

                foreach (var file in files)
                {
                    string fileName = ContentDispositionHeaderValue.Parse(file.ContentDisposition).FileName
                        .ToString()
                        .Trim('"');

                    fileName = this.EnsureCorrectFilename(fileName);

                    obj.VideoUrl = this.GetDbPathAndFilename(fileName);

                    byte[] bufferBytes = new byte[(500 * 1024) * 1024];
                    using (FileStream output = System.IO.File.Create(this.GetPathAndFilename(fileName)))
                    {
                        using (Stream input = file.OpenReadStream())
                        {
                            long totalReadBytes = 0;
                            int readBytes;

                            while ((readBytes = await input.ReadAsync(bufferBytes, 0, bufferBytes.Length)) > 0)
                            {
                                await output.WriteAsync(bufferBytes, 0, readBytes);
                                totalReadBytes += readBytes;
                                IProgress = (int)((float)totalReadBytes / (float)totalBytes * 100.0);
                                await Task.Delay(10);
                            }
                        }
                    }
                }
                _unitOfWork.RoadChaplainIntroductionVideo.Add(obj);
                _unitOfWork.Save();
            }
            return RedirectToAction("Index");
        }

        public IActionResult Delete(int? id)
        {
            if (id == null || id == 0)
            {
                return NotFound();
            }

            var objFromDb = _unitOfWork.RoadChaplainIntroductionVideo.GetFirstOrDefault(x => x.Id == id);
            if (objFromDb == null)
            {
                return NotFound();
            }

            return View(objFromDb);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeletePost(int? id)
        {
            var obj = _unitOfWork.RoadChaplainIntroductionVideo.GetFirstOrDefault(x => x.Id == id);
            if (obj is null)
            {
                return NotFound();
            }

            try
            {
                var webRootPath = StaticUtilities.GetCurrentDirectory();
                var filePath = webRootPath + obj.VideoUrl;
                System.IO.File.Delete(filePath);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }

            _unitOfWork.RoadChaplainIntroductionVideo.Remove(obj);
            _unitOfWork.Save();
            TempData["success"] = "Video Deleted";
            return RedirectToAction("Index");
        }

        #region Utility Functions

        [HttpPost]
        public ActionResult Progress()
        {
            return this.Content(IProgress.ToString());
        }

        private string EnsureCorrectFilename(string filename)
        {
            if (filename.Contains("\\"))
                filename = filename.Substring(filename.LastIndexOf("\\", StringComparison.Ordinal) + 1);

            return filename;
        }

        private string GetPathAndFilename(string filename)
        {
            string path = StaticUtilities.GetCurrentDirectory() + "\\videos\\";

            if (!Directory.Exists(path))
                Directory.CreateDirectory(path);

            return path + filename;
        }

        private string GetDbPathAndFilename(string fileName)
        {
            string path = "/videos/";

            return path + fileName;
        }

        #endregion
    }
}
