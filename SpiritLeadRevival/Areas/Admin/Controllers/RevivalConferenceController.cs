using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin)]

    public class RevivalConferenceController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;
        public RevivalConferenceController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public IActionResult Index()
        {
            var localTimeZone = TimeZoneInfo.Local;
            var objFromDb = _unitOfWork.RevivalConference.GetAll();

            foreach (RevivalConference conference in objFromDb)
            {
                var offset = localTimeZone.GetUtcOffset(conference.EventDateTime);
                if (offset.Hours < 0)
                {
                    //Math.Abs(offset.Hours);
                    var adjustedTime = conference.EventDateTime.AddHours(Math.Abs(offset.Hours));
                    conference.EventDateTime = adjustedTime;
                }
                else if (offset.Hours > 0)
                {
                    //Math.Abs(offset.Hours)*(-1);
                    var adjustedTime = conference.EventDateTime.AddHours(Math.Abs(offset.Hours) * (-1));
                    conference.EventDateTime = adjustedTime;
                }


            }
            return View(objFromDb);
        }

        [HttpGet]
        public IActionResult Create()
        {
            return View();
        }

        //POST
        [HttpPost, ActionName("Create")]
        [ValidateAntiForgeryToken]
        public IActionResult Create(RevivalConference obj)
        {
            if (ModelState.IsValid)
            {
                _unitOfWork.RevivalConference.Add(obj);
                _unitOfWork.Save();
                return RedirectToAction("Index");
            }
            return View(obj);
        }

        [HttpGet]
        public IActionResult Edit(int? id)
        {
            if (id == null || id == 0) return NotFound();
            var objFromDb = _unitOfWork.RevivalConference.GetFirstOrDefault(x => x.Id == id);
            if (objFromDb == null) return NotFound();
            return View(objFromDb);
        }

        //POST
        [HttpPost, ActionName("Edit")]
        [ValidateAntiForgeryToken]
        public IActionResult Edit(RevivalConference obj)
        {
            _unitOfWork.RevivalConference.Update(obj);
            _unitOfWork.Save();
            return RedirectToAction("Index");
        }

        [HttpGet]
        public IActionResult Delete(int? id)
        {
            if (id == null || id == 0) return NotFound();
            var objFromDb = _unitOfWork.RevivalConference.GetFirstOrDefault(x => x.Id == id);
            if (objFromDb == null) return NotFound();
            return View(objFromDb);
        }

        //POST
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult Delete(RevivalConference obj)
        {
            _unitOfWork.RevivalConference.Remove(obj);
            _unitOfWork.Save();
            return RedirectToAction("Index");
        }
    }
}
