using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers;
[Area("Admin")]
[Authorize(Roles = StaticDetails.RoleSystemAdmin)]
public class CarouselItemController : Controller
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IWebHostEnvironment _hostEnvironment;

    public CarouselItemController(IUnitOfWork unitOfWork, IWebHostEnvironment hostEnvironment)
    {
        _unitOfWork = unitOfWork;
        _hostEnvironment = hostEnvironment;
    }
    public IActionResult Index()
    {
        IEnumerable<CarouselItem> objList = _unitOfWork.CarouselItem.GetAll();
        return View(objList);
    }

    [HttpGet]
    public IActionResult Upcert(int? id)
    {
        if (id == null || id == 0)
        {
            var carouselItem = new CarouselItem();
            return View(carouselItem);
        }
        else
        {
            var carouselItem = _unitOfWork.CarouselItem.GetFirstOrDefault(x => x.Id == id);
            return View(carouselItem);
        }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    public IActionResult Upcert(CarouselItem obj, IFormFile? file)
    {
        if (ModelState.IsValid)
        {
            string wwwRootPath = StaticUtilities.GetCurrentDirectory();
            if (file != null)
            {
                string fileName = Guid.NewGuid().ToString();
                var uploads = Path.Combine(wwwRootPath, @"images\carouselItems");
                var extenstion = Path.GetExtension(file.FileName);

                if (obj.ImageUrl != null)
                {
                    var oldImagePath = Path.Combine(wwwRootPath, obj.ImageUrl.TrimStart('\\'));
                    if (System.IO.File.Exists(oldImagePath))
                    {
                        System.IO.File.Delete(oldImagePath);
                    }
                }

                using (var fileStream = new FileStream(Path.Combine(uploads, fileName + extenstion), FileMode.Create))
                {
                    file.CopyTo(fileStream);
                }

                obj.ImageUrl = @"images/carouselItems/" + fileName + extenstion;
            }

            if (obj.Id == 0)
            {
                _unitOfWork.CarouselItem.Add(obj);
            }
            else
            {
                var old = _unitOfWork.CarouselItem.GetFirstOrDefaultWithNoTracking(x => x.Id == obj.Id);
                obj.ImageUrl = old.ImageUrl;
                _unitOfWork.CarouselItem.Update(obj);
            }
            _unitOfWork.Save();
            TempData["success"] = "Item added/created successfully";
            return RedirectToAction("Index");
        }

        return View(obj);
    }

    public IActionResult Delete(int? id)
    {
        if (id == null || id == 0)
        {
            return NotFound();
        }

        var carouselFromDb = _unitOfWork.CarouselItem.GetFirstOrDefault(x => x.Id == id);
        if (carouselFromDb == null)
        {
            return NotFound();
        }

        return View(carouselFromDb);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public IActionResult DeletePost(int? id)
    {
        var obj = _unitOfWork.CarouselItem.GetFirstOrDefault(x => x.Id == id);
        if (obj is null)
        {
            return NotFound();
        }

        // Attempt to delete associated file if it exists
        if (!string.IsNullOrEmpty(obj.ImageUrl))
        {
            try
            {
                var webRootPath = StaticUtilities.GetCurrentDirectory();
                var filePath = Path.Combine(webRootPath, obj.ImageUrl.TrimStart('/', '\\'));
                if (System.IO.File.Exists(filePath))
                {
                    System.IO.File.Delete(filePath);
                }
            }
            catch (Exception e)
            {
                // Log the error but don't prevent the database deletion
                Console.WriteLine($"Error deleting file for CarouselItem {id}: {e.Message}");
                // Could also log to a proper logging framework here
            }
        }

        _unitOfWork.CarouselItem.Remove(obj);
        _unitOfWork.Save();
        TempData["success"] = "Carousel Deleted";
        return RedirectToAction("Index");
    }
}