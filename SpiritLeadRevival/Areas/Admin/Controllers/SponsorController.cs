using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin)]
    public class SponsorController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IWebHostEnvironment _hostEnvironment;
        public SponsorController(IUnitOfWork unitOfWork, IWebHostEnvironment hostEnvironment)
        {
            _unitOfWork = unitOfWork;
            _hostEnvironment = hostEnvironment;
        }

        public IActionResult Index()
        {
            var objFromDb = _unitOfWork.Sponsor.GetAll();
            return View(objFromDb);
        }

        public IActionResult Upcert(int? id)
        {
            Sponsor helpLink = new();
            if (id == null || id == 0)
            {
                //create product
                //ViewBag.CategoryList = CategoryList;
                //ViewData["CoverTypeList"] = CoverTypeList;
                return View(helpLink);
            }
            else
            {
                helpLink = _unitOfWork.Sponsor.GetFirstOrDefault(u => u.Id == id);
                return View(helpLink);

                //update product
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Upcert(Sponsor obj, IFormFile? file)
        {
            if (ModelState.IsValid)
            {
                string wwwRootPath = StaticUtilities.GetCurrentDirectory();
                if (file != null)
                {
                    string fileName = Guid.NewGuid().ToString();
                    var uploads = Path.Combine(wwwRootPath, @"images\sponsors");
                    var extenstion = Path.GetExtension(file.FileName);

                    if (obj.ImageUrl != null)
                    {
                        var oldImagePath = Path.Combine(wwwRootPath, obj.ImageUrl.TrimStart('\\'));
                        if (System.IO.File.Exists(oldImagePath))
                        {
                            System.IO.File.Delete(oldImagePath);
                        }
                    }

                    using (var fileStream = new FileStream(Path.Combine(uploads, fileName + extenstion), FileMode.Create))
                    {
                        file.CopyTo(fileStream);
                    }

                    obj.ImageUrl = @"images/sponsors/" + fileName + extenstion;
                }

                if (obj.Id == 0)
                {
                    _unitOfWork.Sponsor.Add(obj);
                }
                else
                {
                    var old = _unitOfWork.Sponsor.GetFirstOrDefaultWithNoTracking(x => x.Id == obj.Id);
                    obj.ImageUrl = old.ImageUrl;
                    _unitOfWork.Sponsor.Update(obj);
                }
                _unitOfWork.Save();
                TempData["success"] = "Item added/created successfully";
                return RedirectToAction("Index");
            }

            return View(obj);
        }

        public IActionResult Delete(int? id)
        {
            if (id == null || id == 0)
            {
                return NotFound();
            }

            var objFromDb = _unitOfWork.Sponsor.GetFirstOrDefault(x => x.Id == id);
            if (objFromDb == null)
            {
                return NotFound();
            }

            return View(objFromDb);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeletePost(int? id)
        {
            var obj = _unitOfWork.Sponsor.GetFirstOrDefault(x => x.Id == id);
            if (obj is null)
            {
                return NotFound();
            }

            // Attempt to delete associated file if it exists
            if (!string.IsNullOrEmpty(obj.ImageUrl))
            {
                try
                {
                    var webRootPath = StaticUtilities.GetCurrentDirectory();
                    var filePath = Path.Combine(webRootPath, obj.ImageUrl.TrimStart('/', '\\'));
                    if (System.IO.File.Exists(filePath))
                    {
                        System.IO.File.Delete(filePath);
                    }
                }
                catch (Exception e)
                {
                    // Log the error but don't prevent the database deletion
                    Console.WriteLine($"Error deleting file for Sponsor {id}: {e.Message}");
                    // Could also log to a proper logging framework here
                }
            }

            _unitOfWork.Sponsor.Remove(obj);
            _unitOfWork.Save();
            TempData["success"] = "Sponsor Deleted";
            return RedirectToAction("Index");
        }
    }
}
