using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Models.ViewModels;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")] 
    [Authorize(Policy = StaticDetails.RolePrayerTeam)]
    public class PraiseReportController(IUnitOfWork unitOfWork) : Controller
    {
        public IActionResult Index()
        {
            IEnumerable<PraiseReport> objList = unitOfWork.PraiseReport.GetAll();
            var viewModel = new PrayerRequestVm();
            viewModel.PraiseReports = objList;
            viewModel.Request = default!;
            return View(objList);
        }
        [HttpGet]
        public IActionResult Create()
        {
            return View();
        }

        //POST
        [HttpPost, ActionName("Create")]
        [ValidateAntiForgeryToken]
        public IActionResult Create(PraiseReport obj)
        {
            if (obj.Title == obj.Content)
            {
                ModelState.AddModelError("Content", "The title and content cannot be the same");
            }

            if (ModelState.IsValid)
            {
                obj.DatePosted = DateOnly.FromDateTime(DateTime.Now);
                unitOfWork.PraiseReport.Add(obj);
                unitOfWork.Save();
                TempData["success"] = "Praise Report has been created successfully!";
            }

            return RedirectToAction("Index");
        }

        [HttpGet]
        public IActionResult Edit(int? id)
        {
            if (id == null || id == 0)
            {
                return NotFound();
            }

            var newsFromDb = unitOfWork.PraiseReport.GetFirstOrDefault(x => x.Id == id);

            if (newsFromDb == null)
            {
                return NotFound();
            }

            return View(newsFromDb);
        }

        //POST
        [HttpPost, ActionName("Edit")]
        [ValidateAntiForgeryToken]
        public IActionResult Edit(PraiseReport obj)
        {
            if (obj.Title == obj.Content)
            {
                ModelState.AddModelError("Content", "The title and content cannot be the same");
            }

            if (ModelState.IsValid)
            {
                //obj.DatePosted = DateTime.Now;
                unitOfWork.PraiseReport.Update(obj);
                unitOfWork.Save();
                TempData["success"] = "PraiseReport has been edited successfully!";
            }

            return RedirectToAction("Index");
        }

        public IActionResult Delete(int? id)
        {
            if (id == null || id == 0)
            {
                return NotFound();
            }
            var newsFromDb = unitOfWork.PraiseReport.GetFirstOrDefault(u => u.Id == id);

            if (newsFromDb == null)
            {
                return NotFound();
            }

            return View(newsFromDb);
        }

        //POST
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeletePost(int? id)
        {
            var obj = unitOfWork.PraiseReport.GetFirstOrDefault(u => u.Id == id);
            if (obj == null)
            {
                return NotFound();
            }

            unitOfWork.PraiseReport.Remove(obj);
            unitOfWork.Save();
            TempData["success"] = "PraiseReport deleted successfully";
            return RedirectToAction("Index");

        }
    }
}
