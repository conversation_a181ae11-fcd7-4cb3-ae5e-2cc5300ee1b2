using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;
// ReSharper disable ConditionIsAlwaysTrueOrFalseAccordingToNullableAPIContract

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin)]
    public class BoardMemberController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;
        // ReSharper disable once NotAccessedField.Local
        private readonly IWebHostEnvironment _webHostEnvironment;

        public BoardMemberController(IWebHostEnvironment webHostEnvironment, IUnitOfWork unitOfWork)
        {
            _webHostEnvironment = webHostEnvironment;
            _unitOfWork = unitOfWork;
        }
        public IActionResult Index()
        {
            var objList = _unitOfWork.BoardMembers.GetAll();
            return View(objList);
        }
        [HttpGet] 
        public IActionResult Upcert(int? id)
        {
            if (id is null or 0)
            {
                var boardMember = new BoardMember();
                return View(boardMember);
            }
            else
            {
                var objFromDb = _unitOfWork.BoardMembers.GetFirstOrDefault(x => x.Id == id);
                return View(objFromDb);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Upcert(BoardMember obj, IFormFile? file)
        {
            if (ModelState.IsValid)
            {
                string wwwRootPath = StaticUtilities.GetCurrentDirectory();

                if (file != null)
                {
                    var fileName = Guid.NewGuid();
                    var uploads = Path.Combine(wwwRootPath, @"images\boardMembers");
                    var extenstion = Path.GetExtension(file.FileName);

                    if (obj.ImageUrl != null || obj.ImageUrl == "")
                    {
                        var oldImagePath = Path.Combine(wwwRootPath, obj.ImageUrl.TrimStart('\\'));
                        if (System.IO.File.Exists(oldImagePath))
                        {
                            System.IO.File.Delete(oldImagePath);
                        }
                    }

                    using (var fileStream = new FileStream(Path.Combine(uploads, fileName + extenstion), FileMode.Create))
                    {
                        file.CopyTo(fileStream);
                    }

                    obj.ImageUrl = @"images/boardMembers/" + fileName + extenstion;


                }
                if (obj.Id == 0)
                {
                    _unitOfWork.BoardMembers.Add(obj);
                }
                else
                {
                    var old = _unitOfWork.BoardMembers.GetFirstOrDefaultWithNoTracking(x => x.Id == obj.Id);
                    obj.ImageUrl = old.ImageUrl;
                    _unitOfWork.BoardMembers.Update(obj);
                }
                _unitOfWork.Save();
                TempData["success"] = "Board Member added/created successfully";
                return RedirectToAction("Index");
            }

            return View(obj);
        }

        public IActionResult Delete(int? id)
        {
            if (id == null || id == 0)
            {
                return NotFound();
            }

            var fromDb = _unitOfWork.BoardMembers.GetFirstOrDefault(x => x.Id == id);
            if (fromDb == null)
            {
                return NotFound();
            }

            return View(fromDb);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeletePost(int? id)
        {
            var obj = _unitOfWork.BoardMembers.GetFirstOrDefault(x => x.Id == id);
            if (obj is null)
            {
                return NotFound();
            }

            // Attempt to delete associated file if it exists
            if (!string.IsNullOrEmpty(obj.ImageUrl))
            {
                try
                {
                    var webRootPath = StaticUtilities.GetCurrentDirectory();
                    var filePath = Path.Combine(webRootPath, obj.ImageUrl.TrimStart('/', '\\'));
                    if (System.IO.File.Exists(filePath))
                    {
                        System.IO.File.Delete(filePath);
                    }
                }
                catch (Exception e)
                {
                    // Log the error but don't prevent the database deletion
                    Console.WriteLine($"Error deleting file for BoardMember {id}: {e.Message}");
                    // Could also log to a proper logging framework here
                }
            }

            _unitOfWork.BoardMembers.Remove(obj);
            _unitOfWork.Save();
            TempData["success"] = "Board Member Deleted";
            return RedirectToAction("Index");
        }
    }
}
