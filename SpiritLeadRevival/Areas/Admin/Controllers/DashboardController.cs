using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin + ", " + StaticDetails.RoleRoadChaplain + ", " + StaticDetails.RolePrayerTeam)]
    public class DashboardController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;

        public DashboardController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public IActionResult Index()
        {
            // Simple dashboard with basic stats
            ViewBag.NewsItemCount = _unitOfWork.NewsItem.GetAll().Count();
            ViewBag.CarouselItemCount = _unitOfWork.CarouselItem.GetAll().Count();
            ViewBag.EventCount = _unitOfWork.Event.GetAll().Count();
            ViewBag.MailingListCount = _unitOfWork.MailingList.GetAll().Count();
            
            return View();
        }
    }
}
