using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin)]
    public class NewsItemController(IUnitOfWork unitOfWork) : Controller
    {
        public IActionResult Index()
        {
            IEnumerable<NewsItem> objList = unitOfWork.NewsItem.GetAll();
            return View(objList);
        }
        [HttpGet]
        public IActionResult Create()
        {
            return View();
        }

        //POST
        [HttpPost, ActionName("Create")]
        [ValidateAntiForgeryToken]
        public IActionResult Create(NewsItem obj)
        {
            if (obj.Title == obj.Content)
            {
                ModelState.AddModelError("Content", "The title and content cannot be the same");
            }

            if (ModelState.IsValid)
            {
                obj.DatePosted = DateTime.Now;
                unitOfWork.NewsItem.Add(obj);
                unitOfWork.Save();
                TempData["success"] = "News Item has been created successfully!";
            }

            return RedirectToAction("Index");
        }

        [HttpGet]
        public IActionResult Edit(int? id)
        {
            if (id == null || id == 0)
            {
                return NotFound();
            }

            var newsFromDb = unitOfWork.NewsItem.GetFirstOrDefault(x => x.Id == id);

            if (newsFromDb == null)
            {
                return NotFound();
            }

            return View(newsFromDb);
        }

        //POST
        [HttpPost, ActionName("Edit")]
        [ValidateAntiForgeryToken]
        public IActionResult Edit(NewsItem obj)
        {
            if (obj.Title == obj.Content)
            {
                ModelState.AddModelError("Content", "The title and content cannot be the same");
            }

            if (ModelState.IsValid)
            {
                //obj.DatePosted = DateTime.Now;
                unitOfWork.NewsItem.Update(obj);
                unitOfWork.Save();
                TempData["success"] = "News Item has been edited successfully!";
            }

            return RedirectToAction("Index");
        }

        public IActionResult Delete(int? id)
        {
            if (id == null || id == 0)
            {
                return NotFound();
            }
            var newsFromDb = unitOfWork.NewsItem.GetFirstOrDefault(u => u.Id == id);

            if (newsFromDb == null)
            {
                return NotFound();
            }

            return View(newsFromDb);
        }

        //POST
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeletePost(int? id)
        {
            var obj = unitOfWork.NewsItem.GetFirstOrDefault(u => u.Id == id);
            if (obj == null)
            {
                return NotFound();
            }

            unitOfWork.NewsItem.Remove(obj);
            unitOfWork.Save();
            TempData["success"] = "News deleted successfully";
            return RedirectToAction("Index");

        }
    }
}
