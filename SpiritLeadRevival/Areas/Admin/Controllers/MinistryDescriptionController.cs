using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin)]
    public class MinistryDescriptionController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;

        public MinistryDescriptionController(IUnitOfWork untiOfWork)
        {
            _unitOfWork = untiOfWork;
        }

        public IActionResult Index()
        {
            var objFromDb = _unitOfWork.MinistryDescription.GetAll();
            return View(objFromDb);
        }
        [HttpGet]
        public IActionResult Create()
        {
            return View();
        }

        //POST
        [HttpPost, ActionName("Create")]
        [ValidateAntiForgeryToken]
        public IActionResult Create(MinistryDescription obj)
        {
            if (ModelState.IsValid)
            {
                _unitOfWork.MinistryDescription.Add(obj);
                _unitOfWork.Save();
                return RedirectToAction("Index");
            }
            else
            {
                return View(obj);
            }
        }
        [HttpGet]
        public IActionResult Update(int? id)
        {
            if (id == null || id.Value == 0) return NotFound();
            var objFromDb = _unitOfWork.MinistryDescription.GetFirstOrDefault(x => x.Id == id);
            if (objFromDb == null) return NotFound();
            return View(objFromDb);
        }
        //POST
        [HttpPost,ActionName("Update")]
        [ValidateAntiForgeryToken]
        public IActionResult Update(MinistryDescription obj)
        {
            if (ModelState.IsValid)
            {
                _unitOfWork.MinistryDescription.Update(obj);
                _unitOfWork.Save();
                TempData["success"] = "Description edited";
                return RedirectToAction("Index");
            }
            return View(obj);
        }

        [HttpGet]
        public IActionResult Delete(int? id)
        {
            if (id == null || id == 0) return NotFound();
            var objFromDb = _unitOfWork.MinistryDescription.GetFirstOrDefault(x => x.Id == id);
            if (objFromDb == null) return NotFound();
            return View(objFromDb);
        }

        //POST
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeletePost(int? id)
        {
            if (id == null || id == 0) return NotFound();
            var objFromDb = _unitOfWork.MinistryDescription.GetFirstOrDefault(x => x.Id == id);
            if (objFromDb == null) return NotFound();
            _unitOfWork.MinistryDescription.Remove(objFromDb);
            _unitOfWork.Save();
            return RedirectToAction("Index");
        }
    }
}
