using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin + ", " +StaticDetails.RoleRoadChaplain)]
    public class ChaplainController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly SlrmDbContext _db;

        public ChaplainController(IWebHostEnvironment webHostEnvironment, IUnitOfWork unitOfWork, SlrmDbContext db)
        {
            _webHostEnvironment = webHostEnvironment;
            _unitOfWork = unitOfWork;
            _db = db;
        }
        public IActionResult Index()
        {
            var objList = _unitOfWork.RoadChaplain.GetAll();
            return View(objList);
        }
        [HttpGet] 
        public IActionResult Upcert(int? id)
        {
            if (id == null || id == 0)
            {
                var roadChaplain = new RoadChaplain();
                return View(roadChaplain);
            }
            else
            {
                var roadChaplainFromDb = _unitOfWork.RoadChaplain.GetFirstOrDefault(x => x.Id == id);
                return View(roadChaplainFromDb);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Upcert(RoadChaplain obj, IFormFile? file)
        {
            if (ModelState.IsValid)
            {
                string wwwRootPath = StaticUtilities.GetCurrentDirectory();

                if (file != null)
                {
                    string fileName = @"roadChaplains" + obj.FirstName + obj.LastName;
                    var uploads = Path.Combine(wwwRootPath, @"images\roadChaplains");
                    var extenstion = Path.GetExtension(file.FileName);

                    if (obj.ImageUrl != null)
                    {
                        var oldImagePath = Path.Combine(wwwRootPath, obj.ImageUrl.TrimStart('\\'));
                        if (System.IO.File.Exists(oldImagePath))
                        {
                            System.IO.File.Delete(oldImagePath);
                        }
                    }

                    using (var fileStream = new FileStream(Path.Combine(uploads, fileName + extenstion), FileMode.Create))
                    {
                        file.CopyTo(fileStream);
                    }

                    obj.ImageUrl = @"images/roadChaplains/" + fileName + extenstion;


                }
                if (obj.Id == 0)
                {
                    _unitOfWork.RoadChaplain.Add(obj);
                }
                else
                {
                    var old = _unitOfWork.RoadChaplain.GetFirstOrDefaultWithNoTracking(x => x.Id == obj.Id);
                    obj.ImageUrl = old.ImageUrl;
                    _unitOfWork.RoadChaplain.Update(obj);
                }
                _unitOfWork.Save();
                TempData["success"] = "Chaplain added/created successfully";
                return RedirectToAction("Index");
            }

            return View(obj);
        }

        public IActionResult Delete(int? id)
        {
            if (id is null or 0)
            {
                return NotFound();
            }

            var fromDb = _unitOfWork.RoadChaplain.GetFirstOrDefault(x => x.Id == id);
            if (fromDb == null)
            {
                return NotFound();
            }

            return View(fromDb);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeletePost(int? id)
        {
            var obj = _unitOfWork.RoadChaplain.GetFirstOrDefault(x => x.Id == id);
            if (obj is null)
            {
                return NotFound();
            }

            // Attempt to delete associated file if it exists
            if (!string.IsNullOrEmpty(obj.ImageUrl))
            {
                try
                {
                    var webRootPath = StaticUtilities.GetCurrentDirectory();
                    var filePath = Path.Combine(webRootPath, obj.ImageUrl.TrimStart('/', '\\'));
                    if (System.IO.File.Exists(filePath))
                    {
                        System.IO.File.Delete(filePath);
                    }
                }
                catch (Exception exception)
                {
                    // Log the error but don't prevent the database deletion
                    Console.WriteLine($"Error deleting file for RoadChaplain {id}: {exception.Message}");
                    // Could also log to a proper logging framework here
                }
            }

            _unitOfWork.RoadChaplain.Remove(obj);
            _unitOfWork.Save();
            TempData["success"] = "Chaplain Deleted";
            return RedirectToAction("Index");
        }
    }
}
