using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class EventController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;
        public EventController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public IActionResult Index()
        {
            return View(_unitOfWork.Event.GetAll());
        }

        [HttpGet]
        public IActionResult Update(int? id) {
            var objFromDb = _unitOfWork.Event.GetFirstOrDefault(x => x.Id == id);
            return View(objFromDb);
        }

        [HttpGet]
        public IActionResult Delete(int? id) {
            var objFromDb = _unitOfWork.Event.GetFirstOrDefault(x => x.Id == id);
            return View(objFromDb);
        }
        
        [HttpGet]
        public IActionResult Create() {  return View(); }

        [HttpPost, ActionName("Create")]
        [ValidateAntiForgeryToken]
        public IActionResult Create(Event obj)
        {
            _unitOfWork.Event.Add(obj);
            _unitOfWork.Save();
            return RedirectToAction("Index");
        }
        [HttpPost, ActionName("Update")]
        [ValidateAntiForgeryToken]
        public IActionResult Update(Event obj)
        {
            _unitOfWork.Event.Update(obj);
            _unitOfWork.Save();
            return RedirectToAction("Index");

        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult Delete(Event obj)
        {
            _unitOfWork.Event.Remove(obj);
            _unitOfWork.Save();
            return RedirectToAction("Index");
        }
    }

}
