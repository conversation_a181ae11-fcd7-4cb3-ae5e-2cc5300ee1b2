using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin)]
    public class RevivalReferenceController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;
        public RevivalReferenceController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public IActionResult Index()
        {
            var objFromDb = _unitOfWork.RevivalReference.GetAll();
            return View(objFromDb);
        }

        [HttpGet]
        public IActionResult Create()
        {
            return View();
        }

        //POST
        [HttpPost, ActionName("Create")]
        [ValidateAntiForgeryToken]
        public IActionResult Create(RevivalReference obj)
        {
            if (ModelState.IsValid)
            {
                obj.DateWritten = DateTime.Now.Date;
                _unitOfWork.RevivalReference.Add(obj);
                _unitOfWork.Save();
                return RedirectToAction("Index");
            }

            return View(obj);
        }

        [HttpGet]
        public IActionResult Edit(int? id)
        {
            if (id is null or 0) return NotFound();
            var objFromDb = _unitOfWork.RevivalReference.GetFirstOrDefault(x => x.Id == id);
            if (objFromDb is null) return NotFound();
            return View(objFromDb);
        }

        //POST
        [HttpPost, ActionName("Edit")]
        [ValidateAntiForgeryToken]
        public IActionResult Edit(RevivalReference obj)
        {
            _unitOfWork.RevivalReference.Update(obj);
            _unitOfWork.Save();
            return RedirectToAction("Index");
        }

        [HttpGet]
        public IActionResult Delete(int? id)
        {
            if (id is null or 0) return NotFound();
            var objFromDb = _unitOfWork.RevivalReference.GetFirstOrDefault(x => x.Id == id);
            if (objFromDb is null) return NotFound();
            return View(objFromDb);
        }

        //POST
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult Delete(RevivalReference obj)
        {
            _unitOfWork.RevivalReference.Remove(obj);
            _unitOfWork.Save();
            return RedirectToAction("Index");
        }
    }
}
