using MailKit.Net.Smtp;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using MimeKit;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class MailingListController(IUnitOfWork unitOfWork, IConfiguration config, IEmailSender emailSender)
        : Controller
    {

        [HttpGet]
        public IActionResult Index()
        {
            return View(unitOfWork.MailingList.GetAll());
        }
        [HttpGet]
        public IActionResult Update(int? id)
        {
            var objFromDb = unitOfWork.MailingList.GetFirstOrDefault(x => x.Id == id);
            return View(objFromDb);
        }

        [HttpGet]
        public IActionResult Delete(int? id)
        {
            var objFromDb = unitOfWork.MailingList.GetFirstOrDefault(x => x.Id == id);
            return View(objFromDb);
        }

        [HttpGet]
        public IActionResult Create() { return View(); }

        [HttpPost, ActionName("Create")]
        [ValidateAntiForgeryToken]
        public IActionResult Create(MailingListContact obj)
        {
            unitOfWork.MailingList.Add(obj);
            unitOfWork.Save();
            return RedirectToAction("Index");
        }

        [HttpPost, ActionName("Update")]
        [ValidateAntiForgeryToken]
        public IActionResult Update(MailingListContact obj)
        {
            unitOfWork.MailingList.Update(obj);
            unitOfWork.Save();
            return RedirectToAction("Index");

        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult Delete(MailingListContact obj)
        {
            unitOfWork.MailingList.Remove(obj);
            unitOfWork.Save();
            return RedirectToAction("Index");
        }

        [HttpGet]
        public IActionResult BulkUpload()
        {
            return View("BulkUpload");
        }

        [HttpPost, ActionName("BulkUpload")]
        [ValidateAntiForgeryToken]
        public IActionResult BulkUpload(IEnumerable<MailingListContact> obj)
        {
            unitOfWork.MailingList.AddRange(obj);
            unitOfWork.Save();
            return RedirectToAction("Index");
        }

        public IActionResult Send()
        {
            return View("Send");
        }

        [HttpPost, ActionName("Send")]
        public async Task<IActionResult> Send(MailingListEmail email)
        {
            var recipients = unitOfWork.MailingList.GetAll();

            foreach (var recipient in recipients)
            {
                if (recipient.UnsubscribeGuid is not null)
                {
                    // Create the unsubscribe link
                    var unsubscribeLink = Url.Action("Unsubscribe", "MailingList",
                        new { guid = recipient.UnsubscribeGuid }, Request.Scheme);

                    if (unsubscribeLink != null)
                    {
                        if (unsubscribeLink.Contains("localhost"))
                        {
                            unsubscribeLink = unsubscribeLink.Replace("localhost", "spiritleadrevival.org");
                        }

                        // Append the unsubscribe link to the email message
                        var messageWithUnsubscribeLink =
                            email.Message + $"<br/><br/><a href='{unsubscribeLink}'>Unsubscribe</a>";

                        await emailSender.SendEmailAsync(recipient.EmailAddress, email.Subject,
                            (new TextPart(MimeKit.Text.TextFormat.Html)
                            {
                                Text = messageWithUnsubscribeLink
                            }).ToString());
                    }
                    else
                    {
                        await emailSender.SendEmailAsync(recipient.EmailAddress, email.Subject,
                            (new TextPart(MimeKit.Text.TextFormat.Html)
                            {
                                Text = email.Message
                            }).ToString());
                    }
                }
                else
                {
                    await emailSender.SendEmailAsync(recipient.EmailAddress, email.Subject,
                        (new TextPart(MimeKit.Text.TextFormat.Html)
                        {
                            Text = email.Message
                        }).ToString());
                }
            }

            return RedirectToAction(nameof(this.Index));
        }

        [HttpGet, ActionName("Unsubscribe")]
        public async Task<IActionResult> Unsubscribe(string guid)
        {
            var contact = unitOfWork.MailingList.GetFirstOrDefault(x => x.UnsubscribeGuid == guid);
            if (contact == null)
            {
                return NotFound();
            }
            unitOfWork.MailingList.Remove(contact);
            unitOfWork.Save();
            return RedirectToAction("Unsubscribed");
        }

        public async Task<IActionResult> Unsubscribed()
        {
            return View();
        }
    }

}
