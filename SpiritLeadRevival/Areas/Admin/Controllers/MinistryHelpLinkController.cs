using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;
using SpiritLeadRevival.Utilities;

namespace SpiritLeadRevival.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = StaticDetails.RoleSystemAdmin)]
    public class MinistryHelpLinkController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IWebHostEnvironment _hostEnvironment;

        public MinistryHelpLinkController(IUnitOfWork untiOfWork, IWebHostEnvironment hostEnvironment)
        {
            _unitOfWork = untiOfWork;
            _hostEnvironment = hostEnvironment;
        }

        public IActionResult Index()
        {
            var objFromDb = _unitOfWork.MinistryHelpLink.GetAll();
            return View(objFromDb);
        }
        [HttpGet]
        public IActionResult Upcert(int? id)
        {
            MinistryHelpLink helpLink = new();
            if (id == null || id == 0)
            {
                //create product
                //ViewBag.CategoryList = CategoryList;
                //ViewData["CoverTypeList"] = CoverTypeList;
                return View(helpLink);
            }
            else
            {
                helpLink = _unitOfWork.MinistryHelpLink.GetFirstOrDefault(u => u.Id == id);
                return View(helpLink);

                //update product
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Upcert(MinistryHelpLink obj, IFormFile? file)
        {
            if (ModelState.IsValid)
            {
                string wwwRootPath = StaticUtilities.GetCurrentDirectory();
                if (file != null)
                {
                    string fileName = Guid.NewGuid().ToString();
                    var uploads = Path.Combine(wwwRootPath, @"/images/ministryHelpLinks");
                    var extenstion = Path.GetExtension(file.FileName);

                    if (obj.ImageUrl != null)
                    {
                        var oldImagePath = Path.Combine(wwwRootPath, obj.ImageUrl.TrimStart('\\'));
                        if (System.IO.File.Exists(oldImagePath))
                        {
                            System.IO.File.Delete(oldImagePath);
                        }
                    }

                    using (var fileStream = new FileStream(Path.Combine(uploads, fileName + extenstion), FileMode.Create))
                    {
                        file.CopyTo(fileStream);
                    }

                    obj.ImageUrl = @"/images/ministryHelpLinks/" + fileName + extenstion;
                }

                if (obj.Id == 0)
                {
                    _unitOfWork.MinistryHelpLink.Add(obj);
                }
                else
                {
                    var old = _unitOfWork.MinistryHelpLink.GetFirstOrDefaultWithNoTracking(x => x.Id == obj.Id);
                    obj.ImageUrl = old.ImageUrl;
                    _unitOfWork.MinistryHelpLink.Update(obj);
                }
                _unitOfWork.Save();
                TempData["success"] = "Item added/created successfully";
                return RedirectToAction("Index");
            }

            return View(obj);
        }

        public IActionResult Delete(int? id)
        {
            if (id == null || id == 0)
            {
                return NotFound();
            }

            var carouselFromDb = _unitOfWork.MinistryHelpLink.GetFirstOrDefault(x => x.Id == id);
            if (carouselFromDb == null)
            {
                return NotFound();
            }

            return View(carouselFromDb);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeletePost(int? id)
        {
            var obj = _unitOfWork.MinistryHelpLink.GetFirstOrDefault(x => x.Id == id);
            if (obj is null)
            {
                return NotFound();
            }

            try
            {
                var webRootPath = StaticUtilities.GetCurrentDirectory();
                var filePath = webRootPath + obj.ImageUrl;
                System.IO.File.Delete(filePath);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }

            _unitOfWork.MinistryHelpLink.Remove(obj);
            _unitOfWork.Save();
            TempData["success"] = "Carousel Deleted";
            return RedirectToAction("Index");
        }
    }
}
