@page
@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ExternalLoginModel
@{
    ViewData["Title"] = "Register";
}

<h1>@ViewData["Title"]</h1>
<h2 id="external-login-title">Associate your @Model.ProviderDisplayName account.</h2>
<hr />

<p id="external-login-description" class="text-info">
    You've successfully authenticated with <strong>@Model.ProviderDisplayName</strong>.
    Please enter an email address for this site below and click the Register button to finish
    logging in.
</p>

<div class="row">
    <div class="col-md-4">
        <form asp-page-handler="Confirmation" asp-route-returnUrl="@Model.ReturnUrl" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-floating">
                <input asp-for="Input.Email" class="form-control" autocomplete="email" />
                <label asp-for="Input.Email" class="form-label"></label>
                <span asp-validation-for="Input.Email" class="text-danger"></span>
            </div>
            <button type="submit" class="w-100 btn btn-lg btn-primary">Register</button>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
