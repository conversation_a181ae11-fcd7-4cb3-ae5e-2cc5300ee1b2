@page
@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ExternalLoginsModel
@{
    ViewData["Title"] = "Manage your external logins";
    ViewData["ActivePage"] = ManageNavPages.ExternalLogins;
}

<partial name="_StatusMessage" for="StatusMessage" />
@if (Model.CurrentLogins?.Count > 0)
{
    <h3>Registered Logins</h3>
    <table class="table">
        <tbody>
            @foreach (var login in Model.CurrentLogins)
            {
                <tr>
                    <td id="@($"login-provider-{login.LoginProvider}")">@login.ProviderDisplayName</td>
                    <td>
                        @if (Model.ShowRemoveButton)
                        {
                            <form id="@($"remove-login-{login.LoginProvider}")" asp-page-handler="RemoveLogin" method="post">
                                <div>
                                    <input asp-for="@login.LoginProvider" name="LoginProvider" type="hidden" />
                                    <input asp-for="@login.ProviderKey" name="ProviderKey" type="hidden" />
                                    <button type="submit" class="btn btn-primary" title="Remove this @login.ProviderDisplayName login from your account">Remove</button>
                                </div>
                            </form>
                        }
                        else
                        {
                            @: &nbsp;
                        }
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
@if (Model.OtherLogins?.Count > 0)
{
    <h4>Add another service to log in.</h4>
    <hr />
    <form id="link-login-form" asp-page-handler="LinkLogin" method="post" class="form-horizontal">
        <div id="socialLoginList">
            <p>
                @foreach (var provider in Model.OtherLogins)
                {
                    <button id="@($"link-login-button-{provider.Name}")" type="submit" class="btn btn-primary" name="provider" value="@provider.Name" title="Log in using your @provider.DisplayName account">@provider.DisplayName</button>
                }
            </p>
        </div>
    </form>
}
