@page
@using Microsoft.AspNetCore.Mvc.TagHelpers
@model EmailModel
@{
    ViewData["Title"] = "Manage Email";
    ViewData["ActivePage"] = ManageNavPages.Email;
}

<h3>@ViewData["Title"]</h3>
<partial name="_StatusMessage" for="StatusMessage" />
<div class="row">
    <div class="col-md-6">
        <form id="email-form" method="post">
            <div asp-validation-summary="All" class="text-danger"></div>
            @if (Model.IsEmailConfirmed)
            {
                <div class="form-floating input-group">
                    <input asp-for="Email" class="form-control" disabled />
                        <div class="input-group-append">
                            <span class="h-100 input-group-text text-success font-weight-bold">✓</span>
                        </div>
                    <label asp-for="Email" class="form-label"></label>
                </div>
            }
            else
            {
                <div class="form-floating">
                    <input asp-for="Email" class="form-control" disabled />
                    <label asp-for="Email" class="form-label"></label>
                    <button id="email-verification" type="submit" asp-page-handler="SendVerificationEmail" class="btn btn-link">Send verification email</button>
                </div>
            }
            <div class="form-floating">
                <input asp-for="Input.NewEmail" class="form-control" autocomplete="email" aria-required="true" />
                <label asp-for="Input.NewEmail" class="form-label"></label>
                <span asp-validation-for="Input.NewEmail" class="text-danger"></span>
            </div>
            <button id="change-email-button" type="submit" asp-page-handler="ChangeEmail" class="w-100 btn btn-lg btn-primary">Change email</button>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
