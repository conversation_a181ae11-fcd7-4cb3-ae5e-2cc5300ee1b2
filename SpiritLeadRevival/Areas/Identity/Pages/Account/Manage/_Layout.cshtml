@using Microsoft.AspNetCore.Mvc.TagHelpers
@{
    if (ViewData.TryGetValue("ParentLayout", out var parentLayout))
    {
        Layout = (string)parentLayout!;
    }
    else
    {
        // ReSharper disable once Razor.LayoutNotResolved
        Layout = "/Areas/Identity/Pages/_Layout.cshtml";
    }
}

<h1>Manage your account</h1>

<div>
    <h2>Change your account settings</h2>
    <hr />
    <div class="row">
        <div class="col-md-3">
            <partial name="_ManageNav" />
        </div>
        <div class="col-md-9">
            @RenderBody()
        </div>
    </div>
</div>

@section Scripts {
    @await RenderSectionAsync("Scripts", required: false)
}
