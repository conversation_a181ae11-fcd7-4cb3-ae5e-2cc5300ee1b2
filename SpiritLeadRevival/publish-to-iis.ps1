# PowerShell script to build and publish to IIS via WebDAV
param(
    [Parameter(Mandatory=$true)]
    [string]$WebDAVUrl,
    
    [Parameter(Mandatory=$false)]
    [string]$Username,
    
    [Parameter(Mandatory=$false)]
    [string]$Password
)

Write-Host "Building and Publishing SpiritLead Revival to IIS..." -ForegroundColor Green

# Step 1: Install Node dependencies if needed
if (!(Test-Path "node_modules")) {
    Write-Host "Installing Node.js dependencies..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to install dependencies" -ForegroundColor Red
        exit 1
    }
}

# Step 2: Build modern assets
Write-Host "Building modern UI assets..." -ForegroundColor Yellow
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to build assets" -ForegroundColor Red
    exit 1
}

# Step 3: Build .NET application
Write-Host "Building .NET application..." -ForegroundColor Yellow
dotnet publish -c Release -o ./publish
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to build .NET application" -ForegroundColor Red
    exit 1
}

# Step 4: Copy built assets to publish folder
Write-Host "Copying built assets..." -ForegroundColor Yellow
if (Test-Path "./wwwroot/dist") {
    Copy-Item -Path "./wwwroot/dist" -Destination "./publish/wwwroot/" -Recurse -Force
    Write-Host "Assets copied successfully" -ForegroundColor Green
} else {
    Write-Host "Warning: No built assets found in wwwroot/dist" -ForegroundColor Yellow
}

# Step 5: Publish to WebDAV (if URL provided)
if ($WebDAVUrl) {
    Write-Host "Publishing to WebDAV: $WebDAVUrl" -ForegroundColor Yellow
    
    # You would need to implement WebDAV publishing here
    # This is a placeholder - actual implementation depends on your WebDAV setup
    Write-Host "Note: WebDAV publishing requires additional configuration" -ForegroundColor Yellow
    Write-Host "Please copy contents of ./publish/ to your IIS server" -ForegroundColor Cyan
} else {
    Write-Host "Build completed. Files ready in ./publish/ folder" -ForegroundColor Green
}

Write-Host "Publish process completed!" -ForegroundColor Green
