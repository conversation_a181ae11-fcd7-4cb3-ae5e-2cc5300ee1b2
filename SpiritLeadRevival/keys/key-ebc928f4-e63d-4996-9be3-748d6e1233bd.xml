<?xml version="1.0" encoding="utf-8"?>
<key id="ebc928f4-e63d-4996-9be3-748d6e1233bd" version="1">
  <creationDate>2023-09-10T17:29:05.1783604Z</creationDate>
  <activationDate>2023-09-10T17:29:05.1208753Z</activationDate>
  <expirationDate>2023-12-09T17:29:05.1208753Z</expirationDate>
  <descriptor deserializerType="Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60">
    <descriptor>
      <encryption algorithm="AES_256_CBC" />
      <validation algorithm="HMACSHA256" />
      <masterKey p4:requiresEncryption="true" xmlns:p4="http://schemas.asp.net/2015/03/dataProtection">
        <!-- Warning: the key below is in an unencrypted form. -->
        <value>5DP/qzpF4Q3yfbHOtdQYJQqhgIEyEmvSBLfO5uIQ5WiVHgBa2ciPV1keMeLeW2mOj40AL6WMXHFK4Iz/TDLgNQ==</value>
      </masterKey>
    </descriptor>
  </descriptor>
</key>