<?xml version="1.0" encoding="utf-8"?>
<key id="78cafa67-b692-41f7-b459-c5ff0c154a6b" version="1">
  <creationDate>2024-03-10T05:29:30.6421723Z</creationDate>
  <activationDate>2024-03-10T05:29:30.6019546Z</activationDate>
  <expirationDate>2024-06-08T05:29:30.6019546Z</expirationDate>
  <descriptor deserializerType="Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60">
    <descriptor>
      <encryption algorithm="AES_256_CBC" />
      <validation algorithm="HMACSHA256" />
      <masterKey p4:requiresEncryption="true" xmlns:p4="http://schemas.asp.net/2015/03/dataProtection">
        <!-- Warning: the key below is in an unencrypted form. -->
        <value>r7udj70ZVns+/zp04AkbR+sDX3MwYEjQ8hlOzHcoNlpFhqj3hQpAgoCDwUVFhhlwhXNbcFbY1ph9cmjsuEkoRA==</value>
      </masterKey>
    </descriptor>
  </descriptor>
</key>