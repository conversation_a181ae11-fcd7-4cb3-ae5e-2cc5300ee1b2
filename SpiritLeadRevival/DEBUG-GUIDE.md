# SpiritLead Revival - Debug Guide

This guide shows you how to build and debug your modernized application locally.

## 🚀 **Quick Debug Launch**

### **Option 1: PowerShell Script (Recommended)**
```powershell
# Launch admin panel in debug mode
.\debug-launch.ps1 -Target Admin

# Launch dashboard directly
.\debug-launch.ps1 -Target Dashboard

# Launch home page
.\debug-launch.ps1 -Target Home

# Start server without browser
.\debug-launch.ps1 -Target Admin -NoLaunch
```

### **Option 2: Batch File Menu**
```cmd
debug.bat
```
Then choose from the menu:
1. Admin Panel (Default)
2. Dashboard
3. Home Page
4. Server Only (No Browser)

### **Option 3: Visual Studio**
1. Open project in Visual Studio
2. Select debug profile from dropdown:
   - `SpiritLeadRevival-Debug` (Admin Panel)
   - `Admin-Dashboard-Debug` (Dashboard)
   - `SpiritLeadRevival` (Home Page)
3. Press F5 or click "Start Debugging"

### **Option 4: Visual Studio Code**
1. Open project in VS Code
2. Go to Run and Debug (Ctrl+Shift+D)
3. Select configuration:
   - `SpiritLead Revival - Admin Debug`
   - `SpiritLead Revival - Dashboard Debug`
   - `SpiritLead Revival - Debug`
4. Press F5 or click "Start Debugging"

### **Option 5: Command Line**
```bash
# Using launch profiles
dotnet run --launch-profile SpiritLeadRevival-Debug
dotnet run --launch-profile Admin-Dashboard-Debug

# Direct command
dotnet run --configuration Debug --urls "https://localhost:7093;http://localhost:5093"
```

## 🎯 **Debug Profiles Available**

### **SpiritLeadRevival-Debug**
- **Target**: Admin Panel (`/Admin`)
- **URL**: `https://localhost:7093/Admin`
- **Features**: Full debug logging, detailed errors, hot reload
- **Best for**: Admin interface development

### **Admin-Dashboard-Debug**
- **Target**: Dashboard (`/Admin/Dashboard`)
- **URL**: `https://localhost:7093/Admin/Dashboard`
- **Features**: Enhanced logging, hot reload, dashboard focus
- **Best for**: Dashboard and admin features

### **SpiritLeadRevival**
- **Target**: Home Page (`/`)
- **URL**: `https://localhost:7093`
- **Features**: Standard development settings
- **Best for**: Public site development

## 🔧 **Debug Features Enabled**

### **Environment Variables Set:**
- `ASPNETCORE_ENVIRONMENT=Development`
- `ASPNETCORE_DETAILEDERRORS=true`
- `ASPNETCORE_LOGGING__LOGLEVEL__DEFAULT=Debug`
- `ASPNETCORE_LOGGING__LOGLEVEL__MICROSOFT=Information`
- `ASPNETCORE_LOGGING__LOGLEVEL__SPIRITLEADREVIVAL=Debug`

### **Debug Capabilities:**
- ✅ **Detailed Error Pages** - Full stack traces and error details
- ✅ **Debug Logging** - Comprehensive application logging
- ✅ **Hot Reload** - Changes applied without restart
- ✅ **Browser Launch** - Automatic browser opening
- ✅ **HTTPS Support** - SSL certificate for local development
- ✅ **Responsive Testing** - Modern UI debugging

## 📱 **Testing the Modern UI**

### **Desktop Testing:**
1. **Resize Browser Window** - Test responsive breakpoints
2. **Use Dev Tools** (F12) - Simulate mobile devices
3. **Test Admin Features**:
   - Sidebar collapse/expand
   - Table sorting and searching
   - Form validation
   - Modal interactions

### **Mobile Testing:**
1. **Browser Dev Tools** - Device simulation
2. **Real Device Testing** - Connect to `https://[your-ip]:7093`
3. **Admin Panel Mobile**:
   - Hamburger menu functionality
   - Table-to-card transformation
   - Touch interactions
   - Form usability

## 🛠️ **Debug Script Options**

### **debug-launch.ps1 Parameters:**
```powershell
# Target options
-Target Admin      # Launch admin panel
-Target Dashboard  # Launch dashboard
-Target Home       # Launch home page
-Target Site       # Launch main site

# Additional options
-Clean            # Clean build before launch
-Verbose          # Detailed build output
-NoLaunch         # Start server without browser
```

### **Examples:**
```powershell
# Clean build and launch admin with verbose output
.\debug-launch.ps1 -Target Admin -Clean -Verbose

# Start server only for external testing
.\debug-launch.ps1 -Target Admin -NoLaunch

# Quick dashboard launch
.\debug-launch.ps1 -Target Dashboard
```

## 🔍 **Debugging Tips**

### **Common Issues:**

#### **Port Already in Use:**
```bash
# Check what's using the port
netstat -ano | findstr :7093
netstat -ano | findstr :5093

# Kill the process if needed
taskkill /PID [process-id] /F
```

#### **SSL Certificate Issues:**
```bash
# Trust the development certificate
dotnet dev-certs https --trust
```

#### **Build Errors:**
```bash
# Clean and restore
dotnet clean
dotnet restore
dotnet build --configuration Debug
```

### **Logging and Debugging:**
- **Console Output** - Check terminal for detailed logs
- **Browser Console** - F12 for JavaScript errors
- **Network Tab** - Monitor API calls and resource loading
- **Application Logs** - Check debug output for custom logging

## 🎨 **Modern UI Debug Features**

### **CSS Debugging:**
- **Responsive Breakpoints** - Test at 768px, 992px, 1200px
- **Admin Sidebar** - Test collapse/expand functionality
- **Mobile Cards** - Verify table-to-card transformation
- **Touch Targets** - Ensure buttons are touch-friendly

### **JavaScript Debugging:**
- **Modern Features** - ES6+ syntax and modules
- **SweetAlert2** - Modern notification system
- **Form Validation** - Real-time validation feedback
- **File Uploads** - Image preview functionality

## 📊 **Performance Monitoring**

### **Development Tools:**
- **Browser DevTools** - Performance tab
- **Network Monitoring** - Resource loading times
- **Memory Usage** - Check for memory leaks
- **Console Warnings** - Address any warnings

### **Modern UI Performance:**
- **CDN Resources** - Bootstrap, jQuery, SweetAlert2 from CDN
- **Optimized CSS** - Efficient selectors and animations
- **Responsive Images** - Proper sizing and loading
- **Minimal JavaScript** - Clean, efficient code

## 🚀 **Hot Reload**

Hot reload is enabled for:
- ✅ **Razor Views** - Instant view updates
- ✅ **CSS Changes** - Live style updates
- ✅ **JavaScript** - Immediate script changes
- ✅ **Static Files** - Asset updates

## 📞 **Debug Support**

### **If Debug Launch Fails:**
1. **Check Prerequisites**:
   - .NET 8.0 SDK installed
   - PowerShell available
   - Ports 7093/5093 available

2. **Try Alternative Methods**:
   - Use Visual Studio directly
   - Use command line: `dotnet run`
   - Check firewall settings

3. **Verbose Debugging**:
   ```powershell
   .\debug-launch.ps1 -Target Admin -Verbose
   ```

---

**Your modernized SpiritLead Revival application is ready for debugging!** 🐛

The debug profiles provide comprehensive logging and hot reload capabilities for efficient development of your responsive admin interface.
