<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	  <LangVersion>latest</LangVersion>
	  <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <NoWarn>NU1701;1701</NoWarn>
    <SuppressNETCoreSdkPreviewMessage>true</SuppressNETCoreSdkPreviewMessage>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Common" Version="8.2.2" />
    <PackageReference Include="CommunityToolkit.Diagnostics" Version="8.2.2" />
    <PackageReference Include="Microsoft.AspNet.Identity.Core" Version="2.2.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SpiritLeadRevival.DataAccess\SpiritLeadRevival.DataAccess.csproj" />
    <ProjectReference Include="..\SpiritLeadRevival.Models\SpiritLeadRevival.Models.csproj" />
  </ItemGroup>

</Project>
