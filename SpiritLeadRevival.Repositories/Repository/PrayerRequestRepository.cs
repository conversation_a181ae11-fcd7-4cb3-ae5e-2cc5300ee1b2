using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository;

public class PrayerRequestRepository : Repository<PrayerRequest>, IPrayerRequestRepository
{
    private readonly SlrmDbContext _db;

    public PrayerRequestRepository(SlrmDbContext db) : base(db)
    {
        _db = db;
    }

    public void Update(PrayerRequest obj)
    {
        _db.PrayerRequests.Update(obj);
    }
}