using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class StaffMemberRepository : Repository<StaffMember>, IStaffMemberRepository {
    private readonly SlrmDbContext _db;
    public StaffMemberRepository(SlrmDbContext db) : base(db) {
        _db = db;
    }
    public void Update(StaffMember obj) {
        _db.StaffMembers.Update(obj);
    }
}