using System.Linq.Expressions;

namespace SpiritLeadRevival.Repositories.Repository.IRepository
{
    public interface IRepository<T> where T : class {
        T GetFirstOrDefault(Expression<Func<T, bool>> filter, string? includeProperties = null);
        T GetFirstOrDefaultWithNoTracking(Expression<Func<T, bool>> filter, string? includeProperties = null);
        //IEnumerable<T> GetTopNumberResults(int numEntries);
        IEnumerable<T> GetAll(Expression<Func<T, bool>>? filter = null, string? includeProperties = null);
        void IsModified(T obj, Expression<Func<T, string>> filter, bool isModified);
        void Add(T entity);
        void Remove(T entity);
        void RemoveRange(IEnumerable<T> entity);
        void AddRange(IEnumerable<T> entity);
    }
}
