namespace SpiritLeadRevival.Repositories.Repository.IRepository; 

public interface IUnitOfWork {
    IBoardMemberRepository BoardMembers {get;}
    INewsItemRepository NewsItem { get; }
    ICarouselItemRepository CarouselItem { get; }
    IDailyScriptureRepository DailyScripture { get; }
    IVideoFileRepository VideoFile { get; }
    IApplicationUserRepository ApplicationUser { get; }
    IRoadChaplainRepository RoadChaplain { get; }
    IPrayerRequestRepository PrayerRequest { get; }
    IPrayerRequestRecipientRepository PrayerRequestRecipient { get; }
    IMinistryDescriptionRepository MinistryDescription { get; }
    IMinistryHelpLinkRepository MinistryHelpLink { get; }
    IRevivalConferenceRepository RevivalConference { get; }
    IRevivalReferenceRepository RevivalReference { get; }
    IRoadChaplainNewsRepository RoadChaplainNews { get; }
    IStaffMemberRepository StaffMembers { get; }
    ISponsorRepository Sponsor { get; }
    IEventRepository Event { get; }
    IRevivalPrincipalOfFaithRepository RevivalPrincipalOfFaith { get; }
    IRevivalSpiritLeadDecisionRepository RevivalSpiritLeadDecision { get; }
    IRoadChaplainIntroductionVideoRepository RoadChaplainIntroductionVideo { get; }
    IRoadChaplainIntroductionRepository RoadChaplainIntroduction { get; }
    IRoadChaplainVideoRepository RoadChaplainVideo { get; }
    IHeadRoadChaplainRepository HeadRoadChaplain { get; }
    IMailingListRepository MailingList { get; }
    IPastorInformationRepository Pastors { get; }
    IPraiseReportRepository PraiseReport { get; }
    void Save();
}