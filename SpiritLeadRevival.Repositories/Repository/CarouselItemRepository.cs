using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository;

public class CarouselItemRepository : Repository<CarouselItem>, ICarouselItemRepository {
    private readonly SlrmDbContext _db;

    public CarouselItemRepository(SlrmDbContext db) : base(db)
    {
        _db = db;
    }

    public void Update(CarouselItem obj) {
        _db.CarouselItems.Update(obj);
    }
}