using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class RevivalSpiritLeadDecisionRepository : Repository<RevivalSpiritLeadDecision> ,IRevivalSpiritLeadDecisionRepository {
    private readonly SlrmDbContext _db;

    public RevivalSpiritLeadDecisionRepository(SlrmDbContext db) : base(db) {
        _db = db;
    }
    public void Update(RevivalSpiritLeadDecision obj) {
        _db.RevivalSpiritLeadDecisions.Update(obj);
    }
}