using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class PastorInformationRepository : Repository<Pastor>, IPastorInformationRepository {
    private readonly SlrmDbContext _db;
    public PastorInformationRepository(SlrmDbContext db) : base(db) {
        _db = db;
    }
    public void Update(Pastor member) {
        _db.Pastors.Update(member);
    }
}