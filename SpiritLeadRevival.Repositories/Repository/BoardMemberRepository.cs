using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class BoardMemberRepository : Repository<BoardMember>, IBoardMemberRepository {
    private readonly SlrmDbContext _db;
    public BoardMemberRepository(SlrmDbContext db) : base(db) {
        _db = db;
    }
    public void Update(BoardMember member) {
        _db.BoardMembers.Update(member);
    }
}