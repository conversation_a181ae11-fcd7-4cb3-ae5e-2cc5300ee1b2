using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class MinistryHelpLinkRepository : Repository<MinistryHelpLink>, IMinistryHelpLinkRepository {
    private readonly SlrmDbContext _db;

    public MinistryHelpLinkRepository(SlrmDbContext db):base(db) {
        _db = db;
    }

    public void Update(MinistryHelpLink obj) {
        _db.MinistryHelpLinks.Update(obj);
    }
}