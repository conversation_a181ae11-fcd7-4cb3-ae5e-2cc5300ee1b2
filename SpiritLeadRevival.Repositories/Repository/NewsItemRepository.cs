using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class NewsItemRepository : Repository<NewsItem>, INewsItemRepository {
    private readonly SlrmDbContext _db;

    public NewsItemRepository(SlrmDbContext db) :base(db) {
        _db = db;
    }

    public void Update(NewsItem obj) {
        _db.NewsItems.Update(obj);
    }

    public IEnumerable<NewsItem> GetTopNumberResults(int numEntries)
    {
        IQueryable<NewsItem> query = DbSet;
        query = query.OrderByDescending(i => i.Id).Take(numEntries);

        return query.ToList();
    }

    public NewsItem GetLatest()
    {
        return DbSet.OrderByDescending(x => x.Id).First();
    }
}