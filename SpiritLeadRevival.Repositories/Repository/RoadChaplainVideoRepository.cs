using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class RoadChaplainVideoRepository : Repository<RoadChaplainVideo>, IRoadChaplainVideoRepository {
    private readonly SlrmDbContext _db;
    public RoadChaplainVideoRepository(SlrmDbContext db) : base(db) => _db = db;
    public void Update(RoadChaplainVideo obj) {
        _db.RoadChaplainVideos.Update(obj);
    }
    public IEnumerable<RoadChaplainVideo> GetLast(int number)
    {
        var objFromDb = _db.RoadChaplainVideos.ToList();
        var topSelection = objFromDb.OrderByDescending(d => d.Id).Take(number).ToList();
        return topSelection;
    }
}