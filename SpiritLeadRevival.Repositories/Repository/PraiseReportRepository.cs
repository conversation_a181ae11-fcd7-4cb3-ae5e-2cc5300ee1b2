using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository;

public class PraiseReportRepository: Repository<PraiseReport>, IPraiseReportRepository
{
    private readonly SlrmDbContext _db;

    public PraiseReportRepository(SlrmDbContext db) : base(db)
    {
        _db = db;
    }

    public void Update(PraiseReport obj)
    {
        _db.PraiseReports.Update(obj); 
    }

    public IEnumerable<PraiseReport> GetTopNumberOfResults(int numberOfResults)
    {
        var results = _db.PraiseReports.Take(numberOfResults).ToList();

        return results;
    }
}