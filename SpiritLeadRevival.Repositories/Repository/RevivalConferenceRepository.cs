using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class RevivalConferenceRepository :Repository<RevivalConference> ,IRevivalConferenceRepository {
    private readonly SlrmDbContext _db;

    public RevivalConferenceRepository(SlrmDbContext db) : base(db) {
        _db = db;
    }
    public void Update(RevivalConference obj) {
        _db.RevivalConferences.Update(obj);
    }
}