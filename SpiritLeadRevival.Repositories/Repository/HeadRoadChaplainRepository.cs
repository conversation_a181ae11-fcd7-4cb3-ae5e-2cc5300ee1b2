using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class HeadRoadChaplainRepository : Repository<HeadRoadChaplain>, IHeadRoadChaplainRepository {
    private readonly SlrmDbContext _db;
    public HeadRoadChaplainRepository(SlrmDbContext db) : base(db) {
        _db = db;
    }
    public void Update(HeadRoadChaplain obj) {
        _db.HeadRoadChaplains.Update(obj);
    }
}