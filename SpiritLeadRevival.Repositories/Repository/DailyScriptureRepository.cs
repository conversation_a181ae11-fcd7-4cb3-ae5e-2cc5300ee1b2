using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class DailyScriptureRepository : Repository<DailyScripture>, IDailyScriptureRepository {
    private readonly SlrmDbContext _db;

    public DailyScriptureRepository(SlrmDbContext db) : base(db) {
        _db = db;
    }

    public void Update(DailyScripture obj) {
        _db.DailyScriptures.Update(obj);
    }
}