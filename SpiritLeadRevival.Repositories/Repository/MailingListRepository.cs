using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository;

public class MailingListRepository : Repository<MailingListContact>, IMailingListRepository
{
    private readonly SlrmDbContext _db;

    public MailingListRepository(SlrmDbContext db) :base(db) {
        _db = db;
    }

    public void Update(MailingListContact obj) {
        _db.MailingList.Update(obj);
    }
}