using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class RoadChaplainIntroductionVideoRepository : Repository<RoadChaplainIntroductionVideo>, IRoadChaplainIntroductionVideoRepository {
    private readonly SlrmDbContext _db;
    public RoadChaplainIntroductionVideoRepository(SlrmDbContext db) : base(db) => _db = db;
    public void Update(RoadChaplainIntroductionVideo obj) {
        _db.RoadChaplainIntroductionVideos.Update(obj);
    }
    public IEnumerable<RoadChaplainIntroductionVideo> GetLast(int number)
    {
        var objFromDb = _db.RoadChaplainIntroductionVideos.ToList();
        var topSelection = objFromDb.OrderByDescending(d => d.Id).Take(number).ToList();
        return topSelection;
    }
}