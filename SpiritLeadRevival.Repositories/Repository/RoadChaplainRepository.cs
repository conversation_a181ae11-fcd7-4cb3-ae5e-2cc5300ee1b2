using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class RoadChaplainRepository : Repository<RoadChaplain>, IRoadChaplainRepository {
    private readonly SlrmDbContext _db;
    public RoadChaplainRepository(SlrmDbContext db) : base(db) {
        _db = db;
    }
    public void Update(RoadChaplain obj) {
        _db.RoadChaplains.Update(obj);
    }
}