using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class RevivalReferenceRepository : Repository<RevivalReference>, IRevivalReferenceRepository {
    private readonly SlrmDbContext _db;

    public RevivalReferenceRepository(SlrmDbContext db) : base(db) {
        _db = db;
    }

    public void Update(RevivalReference obj) {
        _db.RevivalReferences.Update(obj);
    }
}