using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class SponsorRepository :Repository<Sponsor> , ISponsorRepository {
    private readonly SlrmDbContext _db;
    public SponsorRepository(SlrmDbContext db) : base(db) {
        _db = db;
    }
    public void Update(Sponsor obj) {
        _db.Sponsors.Update(obj);
    }
}