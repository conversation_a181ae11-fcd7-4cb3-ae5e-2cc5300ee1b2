using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class RevivalPrincipalOfFaithRepository : Repository<RevivalPrincipalOfFaith> , IRevivalPrincipalOfFaithRepository {
    private readonly SlrmDbContext _db;

    public RevivalPrincipalOfFaithRepository(SlrmDbContext db) : base(db) {
        _db = db;
    }
    public void Update(RevivalPrincipalOfFaith obj) {
        _db.RevivalPrincipalsOfFaiths.Update(obj);
    }
}