using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class RoadChaplainNewsRepository : Repository<RoadChaplainNews>, IRoadChaplainNewsRepository {
    private readonly SlrmDbContext _db;

    public RoadChaplainNewsRepository(SlrmDbContext db) : base(db) => _db = db;

    public void Update(RoadChaplainNews obj) {
        _db.RoadChaplainNews.Update(obj);
    }
    public IEnumerable<RoadChaplainNews> GetLast(int number)
    {
        var objFromDb = _db.RoadChaplainNews.ToList();
        var topSelection = objFromDb.OrderByDescending(d => d.DatePosted).Take(number).ToList();
        return topSelection;
    }
}