using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository
{
    internal class EventRepository : Repository<Event>, IEventRepository
    {
        private readonly SlrmDbContext _db;
        public EventRepository(SlrmDbContext db) : base(db)
        {
            _db = db;
        }

        public void Update(Event obj)
        {
            _db.Events.Update(obj);
        }
        public IEnumerable<Event> GetTopNumberResults(int numEntries)
        {
            IQueryable<Event> query = DbSet;
            query = query.OrderByDescending(i => i.Id).Take(numEntries);

            return query.ToList();
        }
    }
}