using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class VideoFileRepository : Repository<VideoFile>, IVideoFileRepository {
    private readonly SlrmDbContext _db;
    public VideoFileRepository(SlrmDbContext db) : base(db) => _db = db;
    public void Update(VideoFile obj) {
        _db.VideoFiles.Update(obj);
    }
    public IEnumerable<VideoFile> GetTopNumberResults(int numEntries)
    {
        IQueryable<VideoFile> query = DbSet;
        query = query.OrderByDescending(i => i.Id).Take(numEntries);

        return query.ToList();
    }
}