using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class RoadChaplainIntroductionRepository : Repository<RoadChaplainIntroduction> , IRoadChaplainIntroductionRepository {
    private readonly SlrmDbContext _db;
    public RoadChaplainIntroductionRepository(SlrmDbContext db) : base(db) {
        _db = db;
    }
    public void Update(RoadChaplainIntroduction obj) {
        _db.RoadChaplainIntroductions.Update(obj);
    }
}