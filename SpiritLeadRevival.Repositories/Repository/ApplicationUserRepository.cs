using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository;

public class ApplicationUserRepository : Repository<ApplicationUser>, IApplicationUserRepository
{
    private readonly SlrmDbContext _db;

    public ApplicationUserRepository(SlrmDbContext db) :base(db) {
        _db = db;
    }

    public void Update(ApplicationUser obj) {
        _db.ApplicationUsers.Update(obj);
    }
}