using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository;

public class PrayerRequestRecipientRepository : Repository<PrayerRequestRecipient>, IPrayerRequestRecipientRepository
{
    private readonly SlrmDbContext _db;

    public PrayerRequestRecipientRepository(SlrmDbContext db) : base(db)
    {
        _db = db;
    }

    public void Update(PrayerRequestRecipient obj)
    {
        _db.PrayerRequestRecipients.Update(obj);
    }
}