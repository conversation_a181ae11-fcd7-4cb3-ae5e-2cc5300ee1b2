using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Models;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository; 

public class MinistryDescriptionRepository : Repository<MinistryDescription>, IMinistryDescriptionRepository {
    private readonly SlrmDbContext _db;

    public MinistryDescriptionRepository(SlrmDbContext db) : base(db) {
        _db = db;
    }

    public void Update(MinistryDescription obj) {
        _db.MinistryDescriptions.Update(obj);
    }
}