using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository {
    public class Repository<T> : IRepository<T> where T : class {
        private readonly SlrmDbContext _db;
        internal readonly DbSet<T> DbSet;

        protected Repository(SlrmDbContext db) {
            _db = db;
            this.DbSet = _db.Set<T>();
        }

        public void IsModified(T obj, Expression<Func<T, string>> filter, bool isModified) {
            _db.Entry(obj).Property(filter).IsModified = isModified;
        }

        public void Add(T entity) {
            DbSet.Add(entity);
        }


        

        public IEnumerable<T> GetAll(Expression<Func<T, bool>>? filter = null, string? includeProperties = null) {
            IQueryable<T> query = DbSet;
            if (filter != null) {
                query = query.Where(filter);
            }

            if (includeProperties != null) {
                foreach (var includeProp in includeProperties.Split(new[] {','},
                             StringSplitOptions.RemoveEmptyEntries)) {
                    query = query.Include(includeProp);
                }
            }

            return query.ToList();
        }

        

        public T GetFirstOrDefault(Expression<Func<T, bool>> filter, string? includeProperties = null) {
            
            IQueryable<T> query = DbSet;

            query = query.Where(filter);
            if (includeProperties != null) {
                foreach (var includeProp in includeProperties.Split(new[] {','},
                             StringSplitOptions.RemoveEmptyEntries)) {
                    query = query.Include(includeProp);
                }
            }

            return query.FirstOrDefault()!;
        }
        public T GetFirstOrDefaultWithNoTracking(Expression<Func<T, bool>> filter, string? includeProperties = null) {
            IQueryable<T> query = DbSet;

            query = query.Where(filter);
            if (includeProperties != null) {
                foreach (var includeProp in includeProperties.Split(new[] { ',' },
                             StringSplitOptions.RemoveEmptyEntries)) {
                    query = query.Include(includeProp);
                }
            }

            return query.AsNoTrackingWithIdentityResolution().FirstOrDefault()!;
        }

        public void Remove(T entity) {
            DbSet.Remove(entity);
        }

        public void RemoveRange(IEnumerable<T> entity) {
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            DbSet.RemoveRange(entity);
        }

        public void AddRange(IEnumerable<T> entity)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            DbSet.AddRange(entity);
        }
    }
}
