using SpiritLeadRevival.DataAccess.Data;
using SpiritLeadRevival.Repositories.Repository.IRepository;

namespace SpiritLeadRevival.Repositories.Repository;

public class UnitOfWork : IUnitOfWork
{
    private readonly SlrmDbContext _db;

    public UnitOfWork(SlrmDbContext db)
    {
        _db = db;
        Sponsor = new SponsorRepository(_db);
        NewsItem = new NewsItemRepository(_db);
        CarouselItem = new CarouselItemRepository(_db);
        DailyScripture = new DailyScriptureRepository(_db);
        VideoFile = new VideoFileRepository(_db);
        ApplicationUser = new ApplicationUserRepository(_db);
        RoadChaplain = new RoadChaplainRepository(_db);
        PrayerRequest = new PrayerRequestRepository(_db);
        PrayerRequestRecipient = new PrayerRequestRecipientRepository(_db);
        MinistryDescription = new MinistryDescriptionRepository(_db);
        MinistryHelpLink = new MinistryHelpLinkRepository(_db);
        RevivalConference = new RevivalConferenceRepository(_db);
        RevivalReference = new RevivalReferenceRepository(_db);
        RoadChaplainNews = new RoadChaplainNewsRepository(_db);
        StaffMembers = new StaffMemberRepository(_db);
        Event = new EventRepository(_db);
        RevivalPrincipalOfFaith = new RevivalPrincipalOfFaithRepository(_db);
        RevivalSpiritLeadDecision = new RevivalSpiritLeadDecisionRepository(_db);
        RoadChaplainIntroductionVideo = new RoadChaplainIntroductionVideoRepository(_db);
        RoadChaplainIntroduction = new RoadChaplainIntroductionRepository(_db);
        RoadChaplainVideo = new RoadChaplainVideoRepository(_db);
        HeadRoadChaplain = new HeadRoadChaplainRepository(_db);
        MailingList = new MailingListRepository(_db);
        BoardMembers = new BoardMemberRepository(_db);
        Pastors = new PastorInformationRepository(_db);
        PraiseReport = new PraiseReportRepository(_db);
    }
    public IBoardMemberRepository BoardMembers {get;}
    public IPastorInformationRepository Pastors { get; }
    public IHeadRoadChaplainRepository HeadRoadChaplain { get; }
    public IRoadChaplainIntroductionVideoRepository RoadChaplainIntroductionVideo { get; }
    public IStaffMemberRepository StaffMembers { get; }
    public ISponsorRepository Sponsor { get; }
    public INewsItemRepository NewsItem { get; }
    public ICarouselItemRepository CarouselItem { get; }
    public IDailyScriptureRepository DailyScripture { get; }
    public IVideoFileRepository VideoFile { get; }
    public IApplicationUserRepository ApplicationUser { get; }
    public IRoadChaplainRepository RoadChaplain { get; }
    public IPrayerRequestRepository PrayerRequest { get; }
    public IPrayerRequestRecipientRepository PrayerRequestRecipient { get; }
    public IMinistryDescriptionRepository MinistryDescription { get; }
    public IMinistryHelpLinkRepository MinistryHelpLink { get; }
    public IRevivalConferenceRepository RevivalConference { get; }
    public IRevivalReferenceRepository RevivalReference { get; }
    public IRoadChaplainNewsRepository RoadChaplainNews { get; }
    public IEventRepository Event { get; }
    public IRevivalPrincipalOfFaithRepository RevivalPrincipalOfFaith { get; }
    public IRevivalSpiritLeadDecisionRepository RevivalSpiritLeadDecision { get; }
    public IRoadChaplainIntroductionRepository RoadChaplainIntroduction { get; }
    public IRoadChaplainVideoRepository RoadChaplainVideo { get; }
    public IMailingListRepository MailingList { get; }
    public IPraiseReportRepository PraiseReport { get; }

    public void Save()
    {
        _db.SaveChanges();
    }
}