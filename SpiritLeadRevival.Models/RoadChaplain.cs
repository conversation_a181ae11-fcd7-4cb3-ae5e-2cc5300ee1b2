using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace SpiritLeadRevival.Models; 

public class RoadChaplain {
    [Key]
    public int Id { get; set; }
    [Required] public string FirstName { get; set; }
    [Required] public string LastName { get; set; }
    [Required] [DataType(DataType.EmailAddress)] public string Email { get; set; }
    [Required] [DataType(DataType.PhoneNumber)] public string PhoneNumber { get; set; }
    //public string? Biography { get; set; }
    [ValidateNever] public string? ImageUrl { get; set; }
    public int DisplayOrder { get; set; }
    public string? Title { get; set; }
}