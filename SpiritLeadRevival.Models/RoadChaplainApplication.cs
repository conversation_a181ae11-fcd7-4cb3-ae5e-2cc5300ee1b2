namespace SpiritLeadRevival.Models; 

public class RoadChaplainApplication {
    public int Id { get; set; }
    //public byte[] ImageBytes { get; set; }
    public DateOnly DateSubmitted { get; set; }
    //public bool StaffOrRoadChaplain { get; set; }
    public string FullName { get; set; }
    public string Address { get; set; }
    public string City { get; set; }
    public string State { get; set; }
    public string ZipCode { get; set; }
    public string? Country{ get; set; }
    public string Email { get; set; }
    public string? HomePhone { get; set; }
    public string? CellPhone { get; set; }
    public string? Social { get; set; }
    public DateOnly BirthDate { get; set; }
    public bool MarriedBool { get; set; }
    public string? SpousesName { get; set; }
    public DateOnly? DateOfMarriage { get; set; }
    public bool IsItHappy { get; set; }
    public bool EverDivorced { get; set; }
    public string? DivorceExplaination { get; set; }

    
}