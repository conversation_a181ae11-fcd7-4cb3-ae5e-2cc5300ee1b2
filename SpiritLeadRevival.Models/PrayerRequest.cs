using System.ComponentModel.DataAnnotations;

namespace SpiritLeadRevival.Models; 

public class PrayerRequest {
    public int Id { get; set; }
    [Required(ErrorMessage = "Email requires a Subject")]
    public string Subject { get; set; }
    [Required(ErrorMessage = "Email requires a message.")]
    public string Body { get; set; }
    [Required(ErrorMessage = "Please enter your name.")]
    public string FromName { get; set; }
    [Required(ErrorMessage = "Please enter your email so we can respond back if need be.")]
    [DataType(DataType.EmailAddress)]
    public string FromEmail { get; set; }

}