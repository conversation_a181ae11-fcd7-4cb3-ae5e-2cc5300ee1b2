namespace SpiritLeadRevival.Models;

public class MailingListContact
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string EmailAddress { get; set; }
    public string? UnsubscribeGuid { get; set; } = GenerateUnsubscribeString();


    private static string GenerateUnsubscribeString()
    {
        var guid = Guid.NewGuid();
        var base64 = Convert.ToBase64String(guid.ToByteArray());
        return base64;
    }
}