using System.ComponentModel.DataAnnotations;

namespace SpiritLeadRevival.Models;

public class PraiseReport
{
    public int Id { get; set; }
    [Required(ErrorMessage = "Subject is Required.")]
    public string Title { get; set; }
    [Required(ErrorMessage = "Content is required.")]
    public string Content { get; set; }
    public DateOnly DatePosted { get; set; } = DateOnly.FromDateTime(DateTime.Now);
}