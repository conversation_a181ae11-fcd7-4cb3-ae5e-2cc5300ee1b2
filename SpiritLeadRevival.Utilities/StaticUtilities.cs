using System.Text.RegularExpressions;

namespace SpiritLeadRevival.Utilities; 

public static class StaticUtilities {
    public static string GetCurrentDirectory() {
        var contentRoot = Directory.GetCurrentDirectory();
        return contentRoot;
    }

    public static string GetContentPath(string contentPath) {
        var path = GetCurrentDirectory() + contentPath;
        return path;
    }

    public static string FormatPhoneNumber(string phoneNum, string? phoneFormat = null) {
        

        phoneFormat ??= "(###) ###-####";

        // First, remove everything except of numbers
        Regex regexObj = new Regex(@"[^\d]");
        phoneNum = regexObj.Replace(phoneNum, "");

        // Second, format numbers to phone string
        if (phoneNum.Length > 0)
        {
            phoneNum = Convert.ToInt64(phoneNum).ToString(phoneFormat);
        }

        return phoneNum;
    }
}