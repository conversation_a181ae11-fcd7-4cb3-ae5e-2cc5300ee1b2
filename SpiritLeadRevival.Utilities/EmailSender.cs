using MailKit.Net.Smtp;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.Extensions.Configuration;
using RestSharp;
using RestSharp.Authenticators;

namespace SpiritLeadRevival.Utilities;

public class EmailSender(IConfiguration config) : IEmailSender
{
    private readonly IConfiguration _config = config;
    private const string ApiKey = "**************************************************";
    private const string ApiKeyBackup = "**************************************************";

    public async Task SendEmailAsync(string email, string subject, string htmlMessage)
    {
        if (htmlMessage.Contains("Content-Type: text/html; charset=utf-8"))
        {
            htmlMessage = htmlMessage.Replace("Content-Type: text/html; charset=utf-8", "");
        }
        var options = new RestClientOptions("https://api.mailgun.net/v3/")
        {
            Authenticator = new HttpBasicAuthenticator("api", ApiKeyBackup),
            RemoteCertificateValidationCallback = (sender, certificate, chain, policyErrors) => { return true; }
        };
        var request = new RestRequest();
        RestClient client = new(options);
        request.AddParameter("domain", "mg.spiritleadrevival.org", ParameterType.UrlSegment);
        request.Resource = "{domain}/messages";
        request.AddParameter("text", "Text view of this email is not supported. Please view this email in HTML format. Thank you.");

        request.AddParameter("from", "SpiritLead Revival <<EMAIL>>");
#if DEBUG
        request.AddParameter("to", "<EMAIL>");
#else
        request.AddParameter("to", email);
#endif
        request.AddParameter("subject", subject);
        request.AddParameter("html", htmlMessage);
        request.Method = Method.Post;

        var result = await client.ExecuteAsync(request,default);

        if (result.IsSuccessful)
        {
            return;
        }
    }
}