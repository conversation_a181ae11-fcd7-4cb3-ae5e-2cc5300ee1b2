<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	  <LangVersion>latest</LangVersion>
    <SuppressNETCoreSdkPreviewMessage>true</SuppressNETCoreSdkPreviewMessage>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="CommunityToolkit.Common" Version="8.2.2" />
    <PackageReference Include="CommunityToolkit.Diagnostics" Version="8.2.2" />
    <PackageReference Include="MailKit" Version="4.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="8.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.4" />
    <PackageReference Include="NETCore.MailKit" Version="2.1.0" />
    <PackageReference Include="RestSharp" Version="110.2.0" />
    <PackageReference Include="RestSharp.Authenticators" Version="1.1.1" />
  </ItemGroup>

</Project>
